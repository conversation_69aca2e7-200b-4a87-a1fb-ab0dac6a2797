#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Installation Script for OCR Application
اسکریپت تست نصب برای برنامه OCR

This script checks if all required dependencies are properly installed.
این اسکریپت بررسی می‌کند که آیا تمام وابستگی‌های مورد نیاز به درستی نصب شده‌اند.
"""

import sys
import subprocess
import platform

def print_header():
    """Print test header"""
    print("=" * 60)
    print("🔍 OCR Application Installation Test")
    print("🔍 تست نصب برنامه OCR")
    print("=" * 60)
    print(f"🐍 Python Version: {sys.version}")
    print(f"💻 Platform: {platform.system()} {platform.release()}")
    print("=" * 60)

def test_python_version():
    """Test Python version"""
    print("\n📋 Testing Python Version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}: OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro}: Too old (need 3.8+)")
        return False

def test_import(module_name, package_name=None, description=""):
    """Test importing a module"""
    try:
        __import__(module_name)
        status = "✅"
        result = "OK"
        success = True
    except ImportError as e:
        status = "❌"
        result = f"Failed - {str(e)}"
        success = False
    
    display_name = package_name if package_name else module_name
    desc_text = f" ({description})" if description else ""
    print(f"{status} {display_name}{desc_text}: {result}")
    return success

def test_tesseract():
    """Test Tesseract OCR installation"""
    print("\n🔍 Testing Tesseract OCR...")
    try:
        import pytesseract
        # Try to get Tesseract version
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract OCR {version}: OK")
        return True
    except Exception as e:
        print(f"❌ Tesseract OCR: Failed - {str(e)}")
        print("   💡 Install Tesseract: https://github.com/tesseract-ocr/tesseract")
        return False

def test_adb():
    """Test ADB installation"""
    print("\n📱 Testing ADB (Android Debug Bridge)...")
    try:
        result = subprocess.run(['adb', 'version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ ADB: {version_line}")
            return True
        else:
            print("❌ ADB: Command failed")
            return False
    except FileNotFoundError:
        print("❌ ADB: Not found in PATH")
        print("   💡 Install Android SDK Platform Tools")
        return False
    except subprocess.TimeoutExpired:
        print("❌ ADB: Command timeout")
        return False
    except Exception as e:
        print(f"❌ ADB: Error - {str(e)}")
        return False

def test_core_libraries():
    """Test core Python libraries"""
    print("\n📚 Testing Core Libraries...")
    results = []
    
    # Built-in libraries
    results.append(test_import("tkinter", "tkinter", "GUI Framework"))
    results.append(test_import("sqlite3", "sqlite3", "Database"))
    results.append(test_import("os", "os", "Operating System"))
    results.append(test_import("sys", "sys", "System"))
    results.append(test_import("subprocess", "subprocess", "Process Management"))
    results.append(test_import("re", "re", "Regular Expressions"))
    results.append(test_import("datetime", "datetime", "Date/Time"))
    results.append(test_import("threading", "threading", "Threading"))
    results.append(test_import("time", "time", "Time Functions"))
    results.append(test_import("math", "math", "Mathematics"))
    results.append(test_import("json", "json", "JSON"))
    results.append(test_import("tempfile", "tempfile", "Temporary Files"))
    results.append(test_import("concurrent.futures", "concurrent.futures", "Concurrency"))
    results.append(test_import("multiprocessing", "multiprocessing", "Multiprocessing"))
    results.append(test_import("queue", "queue", "Queue"))
    
    return all(results)

def test_external_libraries():
    """Test external libraries"""
    print("\n📦 Testing External Libraries...")
    results = []
    
    # Image processing
    results.append(test_import("PIL", "Pillow", "Image Processing"))
    results.append(test_import("cv2", "opencv-python", "Computer Vision"))
    results.append(test_import("numpy", "numpy", "Numerical Computing"))
    
    # OCR
    results.append(test_import("pytesseract", "pytesseract", "OCR Wrapper"))
    
    # System utilities
    results.append(test_import("psutil", "psutil", "System Utilities"))
    results.append(test_import("pyautogui", "pyautogui", "GUI Automation"))
    results.append(test_import("pyperclip", "pyperclip", "Clipboard"))
    
    # Network
    results.append(test_import("requests", "requests", "HTTP Library"))
    
    return all(results)

def test_specific_functionality():
    """Test specific functionality"""
    print("\n⚙️ Testing Specific Functionality...")
    
    # Test PIL image creation
    try:
        from PIL import Image
        img = Image.new('RGB', (100, 100), color='red')
        print("✅ PIL Image Creation: OK")
        pil_ok = True
    except Exception as e:
        print(f"❌ PIL Image Creation: Failed - {str(e)}")
        pil_ok = False
    
    # Test NumPy array creation
    try:
        import numpy as np
        arr = np.array([1, 2, 3])
        print("✅ NumPy Array Creation: OK")
        numpy_ok = True
    except Exception as e:
        print(f"❌ NumPy Array Creation: Failed - {str(e)}")
        numpy_ok = False
    
    # Test OpenCV basic operation
    try:
        import cv2
        import numpy as np
        img = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        print("✅ OpenCV Basic Operation: OK")
        cv2_ok = True
    except Exception as e:
        print(f"❌ OpenCV Basic Operation: Failed - {str(e)}")
        cv2_ok = False
    
    # Test tkinter window creation
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        root.destroy()
        print("✅ Tkinter Window Creation: OK")
        tk_ok = True
    except Exception as e:
        print(f"❌ Tkinter Window Creation: Failed - {str(e)}")
        tk_ok = False
    
    return all([pil_ok, numpy_ok, cv2_ok, tk_ok])

def print_summary(results):
    """Print test summary"""
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY / خلاصه تست")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    failed_tests = total_tests - passed_tests
    
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {failed_tests}/{total_tests}")
    
    if failed_tests == 0:
        print("\n🎉 All tests passed! Ready to run OCR Application!")
        print("🎉 تمام تست‌ها موفق! آماده اجرای برنامه OCR!")
    else:
        print(f"\n⚠️ {failed_tests} test(s) failed. Please install missing dependencies.")
        print(f"⚠️ {failed_tests} تست ناموفق. لطفاً وابستگی‌های گمشده را نصب کنید.")
        print("\n💡 Installation guide: INSTALLATION.md")
        print("💡 راهنمای نصب: INSTALLATION.md")
    
    print("=" * 60)

def main():
    """Main test function"""
    print_header()
    
    results = {}
    
    # Test Python version
    results['python_version'] = test_python_version()
    
    # Test core libraries
    results['core_libraries'] = test_core_libraries()
    
    # Test external libraries
    results['external_libraries'] = test_external_libraries()
    
    # Test Tesseract
    results['tesseract'] = test_tesseract()
    
    # Test ADB (optional)
    results['adb'] = test_adb()
    
    # Test specific functionality
    results['functionality'] = test_specific_functionality()
    
    # Print summary
    print_summary(results)
    
    # Return exit code
    if all(results.values()):
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
