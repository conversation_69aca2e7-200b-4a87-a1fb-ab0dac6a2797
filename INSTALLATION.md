# راهنمای نصب برنامه OCR
# OCR Application Installation Guide

## پیش‌نیازها / Prerequisites

### 1. Python
- **نسخه مورد نیاز**: Python 3.8 یا بالاتر
- **دانلود**: [python.org](https://www.python.org/downloads/)

### 2. Tesseract OCR Engine
موتور OCR برای تشخیص متن از تصاویر

#### Windows:
```bash
# دانلود از لینک زیر:
https://github.com/UB-Mannheim/tesseract/wiki

# یا استفاده از Chocolatey:
choco install tesseract

# یا استفاده از Scoop:
scoop install tesseract
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-fas  # برای فارسی
```

#### macOS:
```bash
# استفاده از Homebrew:
brew install tesseract
```

### 3. ADB (Android Debug Bridge) - اختیاری
برای کار با شبیه‌ساز اندروید

#### Windows:
```bash
# دانلود Android SDK Platform Tools:
https://developer.android.com/studio/releases/platform-tools

# اضافه کردن به PATH:
# اضافه کردن مسیر adb.exe به متغیر محیطی PATH
```

#### Linux:
```bash
sudo apt-get install android-tools-adb
```

#### macOS:
```bash
brew install android-platform-tools
```

## نصب کتابخانه‌های Python

### روش 1: استفاده از requirements.txt (توصیه شده)
```bash
# کلون کردن پروژه
git clone <repository-url>
cd ocr_app

# نصب کتابخانه‌ها
pip install -r requirements.txt
```

### روش 2: نصب دستی
```bash
# کتابخانه‌های اصلی
pip install Pillow>=10.0.0
pip install opencv-python>=4.8.0
pip install numpy>=1.24.0
pip install pytesseract>=0.3.10
pip install psutil>=5.9.0
pip install pyautogui>=0.9.54
pip install pyperclip>=1.8.2
pip install requests>=2.31.0
```

### روش 3: استفاده از Virtual Environment (توصیه شده)
```bash
# ایجاد محیط مجازی
python -m venv ocr_env

# فعال‌سازی محیط مجازی
# Windows:
ocr_env\Scripts\activate
# Linux/macOS:
source ocr_env/bin/activate

# نصب کتابخانه‌ها
pip install -r requirements.txt
```

## تنظیمات اضافی

### 1. تنظیم مسیر Tesseract (در صورت نیاز)
اگر Tesseract در مسیر استاندارد نصب نشده، در فایل `ocr_app.py` مسیر را تنظیم کنید:

```python
# Windows:
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Linux/macOS:
pytesseract.pytesseract.tesseract_cmd = '/usr/bin/tesseract'
```

### 2. تنظیم مسیر ADB (در صورت نیاز)
در فایل `ocr_app.py` مسیر ADB را تنظیم کنید:

```python
# Windows:
ADB_PATH = r"C:\path\to\adb.exe"

# Linux/macOS:
ADB_PATH = "/usr/bin/adb"
```

## اجرای برنامه

```bash
# اجرای برنامه
python ocr_app.py
```

## عیب‌یابی / Troubleshooting

### مشکل 1: خطای Tesseract
```
TesseractNotFoundError: tesseract is not installed
```
**راه‌حل**: Tesseract را نصب کنید و مسیر آن را تنظیم کنید.

### مشکل 2: خطای OpenCV
```
ImportError: No module named 'cv2'
```
**راه‌حل**: 
```bash
pip install opencv-python
```

### مشکل 3: خطای PIL/Pillow
```
ImportError: No module named 'PIL'
```
**راه‌حل**:
```bash
pip install Pillow
```

### مشکل 4: خطای NumPy
```
ImportError: No module named 'numpy'
```
**راه‌حل**:
```bash
pip install numpy
```

### مشکل 5: خطای ADB
```
adb.exe یافت نشد
```
**راه‌حل**: ADB را نصب کنید و به PATH اضافه کنید.

## بررسی نصب

برای بررسی صحت نصب، کد زیر را اجرا کنید:

```python
# test_installation.py
try:
    import tkinter as tk
    print("✅ tkinter: OK")
except ImportError:
    print("❌ tkinter: Failed")

try:
    from PIL import Image
    print("✅ Pillow: OK")
except ImportError:
    print("❌ Pillow: Failed")

try:
    import cv2
    print("✅ OpenCV: OK")
except ImportError:
    print("❌ OpenCV: Failed")

try:
    import numpy
    print("✅ NumPy: OK")
except ImportError:
    print("❌ NumPy: Failed")

try:
    import pytesseract
    print("✅ pytesseract: OK")
except ImportError:
    print("❌ pytesseract: Failed")

try:
    import psutil
    print("✅ psutil: OK")
except ImportError:
    print("❌ psutil: Failed")

try:
    import pyautogui
    print("✅ pyautogui: OK")
except ImportError:
    print("❌ pyautogui: Failed")

try:
    import pyperclip
    print("✅ pyperclip: OK")
except ImportError:
    print("❌ pyperclip: Failed")

try:
    import requests
    print("✅ requests: OK")
except ImportError:
    print("❌ requests: Failed")

print("\n🎉 بررسی نصب کامل شد!")
```

## نکات مهم

1. **Python Version**: حتماً از Python 3.8+ استفاده کنید
2. **Virtual Environment**: استفاده از محیط مجازی توصیه می‌شود
3. **Admin Rights**: ممکن است برای نصب برخی کتابخانه‌ها نیاز به دسترسی مدیر باشد
4. **Firewall**: برنامه ممکن است نیاز به دسترسی شبکه داشته باشد
5. **Antivirus**: ممکن است نیاز به اضافه کردن برنامه به لیست استثناء آنتی‌ویروس باشد

## پشتیبانی

در صورت بروز مشکل:
1. ابتدا مراحل نصب را دوباره بررسی کنید
2. نسخه Python و کتابخانه‌ها را چک کنید
3. لاگ خطاها را بررسی کنید
4. از بخش Issues در GitHub استفاده کنید
