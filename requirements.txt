# OCR Application Requirements
# کتابخانه‌های مورد نیاز برای برنامه OCR

# Core GUI Framework
# فریمورک رابط کاربری اصلی
# tkinter - Built-in with Python (no installation needed)

# Image Processing Libraries
# کتابخانه‌های پردازش تصویر
Pillow>=10.0.0              # PIL - Python Imaging Library
opencv-python>=4.8.0        # OpenCV for computer vision
numpy>=1.24.0               # Numerical computing

# OCR Engine
# موتور تشخیص متن
pytesseract>=0.3.10         # Python wrapper for Tesseract OCR

# System and Process Management
# مدیریت سیستم و پردازش‌ها
psutil>=5.9.0               # System and process utilities
pyautogui>=0.9.54           # GUI automation

# Clipboard Operations
# عملیات کلیپ‌بورد
pyperclip>=1.8.2            # Clipboard operations

# HTTP Requests (for API calls)
# درخواست‌های HTTP (برای فراخوانی API)
requests>=2.31.0            # HTTP library for API calls

# Built-in Libraries (No installation needed)
# کتابخانه‌های داخلی پایتون (نیازی به نصب ندارند)
# - tkinter (GUI framework)
# - sqlite3 (Database)
# - os (Operating system interface)
# - sys (System-specific parameters)
# - subprocess (Subprocess management)
# - re (Regular expressions)
# - datetime (Date and time)
# - threading (Threading support)
# - time (Time-related functions)
# - math (Mathematical functions)
# - concurrent.futures (Concurrent execution)
# - multiprocessing (Process-based parallelism)
# - queue (Queue data structure)
# - json (JSON encoder/decoder)
# - tempfile (Temporary files)

# Additional Requirements
# نیازمندی‌های اضافی

# Tesseract OCR Engine
# موتور OCR Tesseract
# Download and install from: https://github.com/tesseract-ocr/tesseract
# Windows: https://github.com/UB-Mannheim/tesseract/wiki
# Linux: sudo apt-get install tesseract-ocr
# macOS: brew install tesseract

# ADB (Android Debug Bridge) - Optional
# پل اشکال‌زدایی اندروید - اختیاری
# Download Android SDK Platform Tools
# Add adb to system PATH
