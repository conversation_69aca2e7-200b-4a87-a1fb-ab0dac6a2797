#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
from tkinter import filedialog
from tkinter import messagebox
import os
import sys
import sqlite3
from tkinter import simpledialog
import subprocess
import re
from datetime import datetime, timedelta
import threading
import time
import math
import pyautogui
from PIL import Image, ImageEnhance, ImageOps
from PIL import Image, ImageEnhance, ImageTk
import pytesseract
import concurrent.futures
import multiprocessing
import psutil

# تنظیم مسیر Tesseract OCR
# مسیر پیش‌فرض Tesseract OCR در ویندوز
pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

# تنظیم مسیر TESSDATA_PREFIX
os.environ['TESSDATA_PREFIX'] = r"C:\Program Files\Tesseract-OCR\tessdata"

# ایجاد دایرکتوری برای ذخیره اسکرین‌شات‌ها
SCREENSHOT_DIR = "screenshots"
os.makedirs(SCREENSHOT_DIR, exist_ok=True)

# مسیر دیتابیس والت‌ها
WALLET_DB_PATH = "wallet_addresses.db"

def check_tesseract_installation():
    """Check if Tesseract OCR is installed correctly"""
    try:
        # Try to get Tesseract version
        version = pytesseract.get_tesseract_version()
        print(f"Tesseract OCR version: {version}")
        return True
    except Exception as e:
        print(f"Error checking Tesseract OCR installation: {e}")

        # Check if tesseract.exe exists
        tesseract_path = pytesseract.pytesseract.tesseract_cmd
        if not os.path.exists(tesseract_path):
            print(f"Tesseract executable not found at: {tesseract_path}")
            print("Please install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")

        # Check if tessdata directory exists
        tessdata_path = os.environ.get('TESSDATA_PREFIX')
        if not tessdata_path or not os.path.exists(tessdata_path):
            print(f"Tessdata directory not found at: {tessdata_path}")
            print("Please make sure the TESSDATA_PREFIX environment variable is set to your 'tessdata' directory.")

        # Check if eng.traineddata exists
        eng_traineddata_path = os.path.join(tessdata_path, "eng.traineddata") if tessdata_path else None
        if not eng_traineddata_path or not os.path.exists(eng_traineddata_path):
            print(f"English language data file not found at: {eng_traineddata_path}")
            print("Please make sure you have installed the English language data.")

        return False

class OCRApplication(tk.Tk):
    def __init__(self):
        super().__init__()

        # Configure the main window
        self.title("OCR برنامه")
        self.geometry("800x600")

        # Center the main window
        self.center_window(800, 600)

        # Initialize database
        self.init_database()

        # Create ttk style
        self.style = ttk.Style()

        # Create a notebook (tabbed interface)
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create the first tab (Scan and Display)
        self.scan_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.scan_tab, text="اسکن و نمایش")
        self.setup_scan_tab()

        # Create the second tab (Settings)
        self.settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_tab, text="تنظیمات")
        self.setup_settings_tab()

        # Initialize auto scan variables
        self.auto_scan_active = False
        self.auto_scan_job = None

        # Configure style for right-to-left text
        self.configure_rtl_style()

        # Bind close event to cleanup
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Load default ports into scan treeview
        self.load_default_ports_to_scan_tree()

        # Update connection status
        self.update_connection_status()

    def center_window(self, width, height):
        """Center window on screen"""
        try:
            # Get screen dimensions
            screen_width = self.winfo_screenwidth()
            screen_height = self.winfo_screenheight()

            # Calculate position
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2

            # Set geometry
            self.geometry(f"{width}x{height}+{x}+{y}")
        except Exception as e:
            print(f"Error centering window: {e}")

    def update_connection_status(self):
        """Update connection status label"""
        try:
            # Get default group
            default_group = self.get_default_group()

            # Count connected and disconnected ports
            self.cursor.execute("""
                SELECT connection_status, COUNT(*)
                FROM ports
                WHERE group_number = ?
                GROUP BY connection_status
            """, (default_group,))

            results = self.cursor.fetchall()

            connected_count = 0
            disconnected_count = 0

            for status, count in results:
                if status == "متصل":
                    connected_count = count
                else:
                    disconnected_count += count

            # Update label
            self.connection_status_label.config(
                text=f"آمار اتصال: {connected_count} متصل، {disconnected_count} قطع"
            )

        except Exception as e:
            self.connection_status_label.config(text="آمار اتصال: خطا در بررسی")
            print(f"Error updating connection status: {e}")

    def configure_rtl_style(self):
        """Configure the application for right-to-left text support"""
        # Try to set the text direction to right-to-left
        try:
            self.tk.call('tk', 'scaling', 1.0)
            self.tk.call('encoding', 'system', 'utf-8')

            # Set font for better Persian text display with normal size
            default_font = ('Tahoma', 12)
            heading_font = ('Tahoma', 14, 'bold')

            style = ttk.Style()
            style.configure('TButton', font=default_font)
            style.configure('TLabel', font=default_font, justify='right')
            style.configure('TNotebook.Tab', font=default_font)
            style.configure('Heading.TLabel', font=heading_font)
            style.configure('Treeview', font=default_font)
            style.configure('Treeview.Heading', font=default_font, justify='center')

        except Exception as e:
            print(f"Error configuring RTL support: {e}")

    def setup_scan_tab(self):
        """Set up the Scan and Display tab with buttons and treeview"""
        # Main container
        main_frame = ttk.Frame(self.scan_tab)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create a PanedWindow to divide the space
        paned = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        paned.pack(fill='both', expand=True)

        # Top frame for buttons (1/3 of the space)
        buttons_frame = ttk.LabelFrame(paned, text="دکمه‌ها")

        # Bottom frame for treeview (2/3 of the space)
        treeview_frame = ttk.LabelFrame(paned, text="نمایش اطلاعات")

        # Add frames to paned window with appropriate weights
        paned.add(buttons_frame, weight=1)
        paned.add(treeview_frame, weight=2)

        # Organize buttons in the top frame
        self.setup_scan_buttons(buttons_frame)

        # Create treeview in the bottom frame
        self.setup_scan_treeview(treeview_frame)

    def setup_scan_buttons(self, parent):
        """Set up buttons in the scan tab"""
        # Create a frame for buttons with a grid layout
        buttons_container = ttk.Frame(parent)
        buttons_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Configure grid with 5 columns
        buttons_container.columnconfigure(0, weight=1)
        buttons_container.columnconfigure(1, weight=1)
        buttons_container.columnconfigure(2, weight=1)
        buttons_container.columnconfigure(3, weight=1)
        buttons_container.columnconfigure(4, weight=1)

        # Add scan button
        self.btn_manual_scan = ttk.Button(
            buttons_container,
            text="🔍 اسکن دستی",
            command=self.start_manual_scan,
            width=15
        )
        self.btn_manual_scan.grid(row=0, column=0, padx=5, pady=5)

        # Add auto scan button
        self.btn_auto_scan = ttk.Button(
            buttons_container,
            text="⏱️ اسکن اتوماتیک",
            command=self.setup_auto_scan,
            width=15
        )
        self.btn_auto_scan.grid(row=0, column=1, padx=5, pady=5)

        # Auto scan is initially off
        self.auto_scan_active = False
        self.auto_scan_job = None

        # Add forecast button
        self.btn_forecast = ttk.Button(
            buttons_container,
            text="🔮 پیش‌بینی",
            command=self.show_forecast_window,
            width=15
        )
        self.btn_forecast.grid(row=0, column=2, padx=5, pady=5)

        # Add show info button
        self.btn_show_info = ttk.Button(
            buttons_container,
            text="📊 نمایش اطلاعات",
            command=self.show_info_window,
            width=15
        )
        self.btn_show_info.grid(row=0, column=3, padx=5, pady=5)

        # Add records button
        self.btn_records = ttk.Button(
            buttons_container,
            text="📝 سوابق",
            command=self.show_records_window,
            width=15
        )
        self.btn_records.grid(row=0, column=4, padx=5, pady=5)

        # Configure grid with 8 columns instead of 7
        buttons_container.columnconfigure(5, weight=1)
        buttons_container.columnconfigure(6, weight=1)
        buttons_container.columnconfigure(7, weight=1)

        # Add robotic scan button
        self.btn_robotic_scan = ttk.Button(
            buttons_container,
            text="🤖 اسکن رباتیک",
            command=self.setup_robotic_scan,
            width=15
        )
        self.btn_robotic_scan.grid(row=0, column=5, padx=5, pady=5)

        # Add wallet scan button
        self.btn_wallet_scan = ttk.Button(
            buttons_container,
            text="💰 اسکن آدرس والت‌ها",
            command=self.scan_all_wallet_addresses,
            width=15
        )
        self.btn_wallet_scan.grid(row=0, column=6, padx=5, pady=5)

        # Add clear treeview button
        self.btn_clear_treeview = ttk.Button(
            buttons_container,
            text="🧹 پاکسازی لیست",
            command=self.clear_scan_treeview,
            width=15
        )
        self.btn_clear_treeview.grid(row=0, column=7, padx=5, pady=5)


        # Add summary frame
        summary_frame = ttk.LabelFrame(buttons_container, text="خلاصه اطلاعات")
        summary_frame.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky='ew')

        # Add total score and dollar value labels
        self.total_score_label = ttk.Label(
            summary_frame,
            text="مجموع امتیازها: 0",
            anchor="center"
        )
        self.total_score_label.pack(side='right', padx=20, pady=5)

        self.total_dollar_label = ttk.Label(
            summary_frame,
            text="ارزش کل به دلار: $0.00",
            anchor="center"
        )
        self.total_dollar_label.pack(side='left', padx=20, pady=5)

        # Add color legend frame
        color_legend_frame = ttk.LabelFrame(buttons_container, text="راهنمای رنگ‌ها")
        color_legend_frame.grid(row=1, column=3, columnspan=4, padx=5, pady=5, sticky='ew')

        # Create a frame for color legends
        color_legends = ttk.Frame(color_legend_frame)
        color_legends.pack(fill='x', padx=5, pady=5)

        # Create color samples with labels
        self.color_legends_frame = color_legends
        self.update_color_legend()

        # Add status label
        self.scan_status_label = ttk.Label(
            buttons_container,
            text="وضعیت: آماده",
            anchor="center"
        )
        self.scan_status_label.grid(row=2, column=0, columnspan=3, padx=5, pady=5, sticky='ew')

        # Add countdown label for next scan
        self.countdown_label = ttk.Label(
            buttons_container,
            text="",
            anchor="center",
            foreground="blue"
        )
        self.countdown_label.grid(row=2, column=3, columnspan=4, padx=5, pady=5, sticky='ew')

        # Add progress bar
        self.scan_progress_var = tk.DoubleVar()
        self.scan_progress_bar = ttk.Progressbar(
            buttons_container,
            variable=self.scan_progress_var,
            maximum=100
        )
        self.scan_progress_bar.grid(row=3, column=0, columnspan=3, padx=5, pady=5, sticky='ew')

        # Add scan time label
        self.scan_time_label = ttk.Label(
            buttons_container,
            text="",
            anchor="center",
            foreground="green"
        )
        self.scan_time_label.grid(row=3, column=3, columnspan=2, padx=5, pady=5, sticky='ew')

        # Add connection status label
        self.connection_status_label = ttk.Label(
            buttons_container,
            text="آمار اتصال: در حال بررسی...",
            anchor="center",
            foreground="blue"
        )
        self.connection_status_label.grid(row=3, column=5, columnspan=2, padx=5, pady=5, sticky='ew')

        # Hide progress bar initially
        self.scan_progress_bar.grid_remove()

        # Store reference to buttons frame for stop button
        self.scan_buttons_frame = buttons_container

        # Initialize scan state tracking
        self.scan_start_time = None
        self.countdown_job = None

        # Initialize style for button colors
        self.style = ttk.Style()
        self.style.configure("AutoActive.TButton", background="orange")
        self.style.configure("RoboticActive.TButton", background="red")
        self.style.configure("ManualActive.TButton", background="green")

        # Initialize thread pool for robotic scanning
        self.thread_pool = None
        self.max_workers = self.calculate_optimal_thread_count()

    def calculate_optimal_thread_count(self):
        """Calculate optimal thread count based on CPU cores and current usage"""
        try:
            # Get system information
            cpu_count = multiprocessing.cpu_count()
            cpu_usage = psutil.cpu_percent(interval=1)
            available_memory_gb = psutil.virtual_memory().available / (1024**3)

            print(f"سیستم: {cpu_count} هسته، استفاده CPU: {cpu_usage}%, حافظه آزاد: {available_memory_gb:.1f}GB")

            # Base thread count on CPU cores
            if cpu_count >= 32:  # 14900K/32 cores/128GB system
                base_threads = min(16, cpu_count // 2)
            elif cpu_count >= 20:  # 13900K system
                base_threads = min(12, cpu_count // 2)
            elif cpu_count >= 8:   # 9700K/64GB system
                base_threads = min(8, cpu_count // 2)
            else:
                base_threads = min(4, cpu_count)

            # Adjust based on CPU usage
            if cpu_usage > 80:
                base_threads = max(2, base_threads // 2)
            elif cpu_usage > 60:
                base_threads = max(3, int(base_threads * 0.75))

            # Adjust based on available memory (each thread needs ~100MB)
            max_memory_threads = int(available_memory_gb * 10)  # 10 threads per GB
            base_threads = min(base_threads, max_memory_threads)

            # Ensure minimum of 2 threads
            optimal_threads = max(2, base_threads)

            print(f"تعداد thread بهینه محاسبه شده: {optimal_threads}")
            return optimal_threads

        except Exception as e:
            print(f"خطا در محاسبه thread count: {e}")
            return 4  # Default fallback

    def start_countdown(self, seconds, scan_type):
        """Start countdown timer for next scan"""
        if self.countdown_job:
            self.after_cancel(self.countdown_job)

        def update_countdown():
            nonlocal seconds
            if seconds > 0:
                self.countdown_label.config(text=f"اسکن {scan_type} بعدی در {seconds} ثانیه")
                seconds -= 1
                self.countdown_job = self.after(1000, update_countdown)
            else:
                self.countdown_label.config(text="")

        update_countdown()

    def update_scan_time_display(self, elapsed_time):
        """Update scan time display"""
        minutes = int(elapsed_time // 60)
        seconds = int(elapsed_time % 60)
        if minutes > 0:
            self.scan_time_label.config(text=f"مدت زمان اسکن: {minutes}:{seconds:02d}")
        else:
            self.scan_time_label.config(text=f"مدت زمان اسکن: {seconds} ثانیه")

    def setup_scan_treeview(self, parent):
        """Set up treeview in the scan tab"""
        # Create a frame for the treeview
        treeview_container = ttk.Frame(parent)
        treeview_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Create columns for the treeview
        columns = ('row', 'port_number', 'score', 'dollar_value', 'ad_count', 'update_time')
        self.scan_tree = ttk.Treeview(treeview_container, columns=columns, show='headings')

        # Define headings with sort functionality
        self.scan_tree.heading('row', text='ردیف',
                              command=lambda: self.treeview_sort_column(self.scan_tree, 'row', False))
        self.scan_tree.heading('port_number', text='شماره پورت',
                              command=lambda: self.treeview_sort_column(self.scan_tree, 'port_number', False))
        self.scan_tree.heading('score', text='امتیاز',
                              command=lambda: self.treeview_sort_column(self.scan_tree, 'score', False))
        self.scan_tree.heading('dollar_value', text='ارزش دلاری',
                              command=lambda: self.treeview_sort_column(self.scan_tree, 'dollar_value', False))
        self.scan_tree.heading('ad_count', text='تعداد تبلیغ',
                              command=lambda: self.treeview_sort_column(self.scan_tree, 'ad_count', False))
        self.scan_tree.heading('update_time', text='تاریخ و زمان بروزرسانی',
                              command=lambda: self.treeview_sort_column(self.scan_tree, 'update_time', False))

        # Define columns
        self.scan_tree.column('row', width=50, anchor='center')
        self.scan_tree.column('port_number', width=100, anchor='center')
        self.scan_tree.column('score', width=100, anchor='center')
        self.scan_tree.column('dollar_value', width=80, anchor='center')
        self.scan_tree.column('ad_count', width=80, anchor='center')
        self.scan_tree.column('update_time', width=150, anchor='center')

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(treeview_container, orient='vertical', command=self.scan_tree.yview)
        self.scan_tree.configure(yscrollcommand=y_scrollbar.set)
        y_scrollbar.pack(side='right', fill='y')

        x_scrollbar = ttk.Scrollbar(treeview_container, orient='horizontal', command=self.scan_tree.xview)
        self.scan_tree.configure(xscrollcommand=x_scrollbar.set)
        x_scrollbar.pack(side='bottom', fill='x')

        self.scan_tree.pack(fill='both', expand=True)

        # Create context menu with better organization
        self.scan_context_menu = tk.Menu(self, tearoff=0)

        # 📱 نمایش و اسکرین گروه
        self.scan_context_menu.add_command(label="📱 نمایش اسکرین", command=self.show_selected_port)
        self.scan_context_menu.add_command(label="📊 نمایش اسکن امتیاز", command=self.show_score_screenshot)
        self.scan_context_menu.add_command(label="📺 نمایش اسکن تبلیغات", command=self.show_ads_screenshot)
        self.scan_context_menu.add_command(label="💾 ذخیره اسکرین", command=self.save_current_screenshot)
        self.scan_context_menu.add_separator()

        # 🔄 عملیات اصلی گروه
        self.scan_context_menu.add_command(label="🔄 اسکن مجدد", command=self.rescan_selected_port)
        self.scan_context_menu.add_command(label="🚀 باز کردن KKT", command=self.open_kkt_for_selected_port)
        self.scan_context_menu.add_command(label="❌ بستن KKT", command=self.close_kkt_app)
        self.scan_context_menu.add_command(label="🔍 تشخیص برنامه فعال", command=self.detect_focused_app_for_selected_port)
        self.scan_context_menu.add_separator()

        # 🎯 تشخیص صفحات (ترتیب چک‌باکس‌ها)
        self.scan_context_menu.add_command(label="🎯 تشخیص صفحه فعلی", command=self.detect_current_screen)
        self.scan_context_menu.add_command(label="🔑 تشخیص دکمه Enter", command=self.detect_enter_for_selected_port)
        self.scan_context_menu.add_command(label="🌐 تشخیص و انتخاب سرور", command=self.detect_and_select_server)
        self.scan_context_menu.add_command(label="🔒 تشخیص Privacy Information", command=self.detect_privacy_for_selected_port)
        self.scan_context_menu.add_command(label="⏳ تشخیص Loading", command=self.detect_loading_for_selected_port)
        self.scan_context_menu.add_command(label="⚫ تشخیص صفحه مشکی", command=self.detect_black_screen_for_selected_port)
        self.scan_context_menu.add_command(label="🔊 تشخیص ایکن بلندگو", command=self.detect_ad_speaker_for_selected_port)
        self.scan_context_menu.add_command(label="💰 تشخیص آدرس والت", command=self.show_wallet_address_detection)
        self.scan_context_menu.add_separator()

        # 🔧 ابزارهای پیشرفته
        self.scan_context_menu.add_command(label="🔧 نمایش کراپ دکمه Enter", command=self.show_enter_button_crop)
        self.scan_context_menu.add_command(label="🔧 نمایش کراپ ایکن بلندگو", command=self.show_ad_speaker_crop)

        # Bind right-click to show context menu
        self.scan_tree.bind("<Button-3>", self.show_scan_context_menu)

        # Add some example data
        from datetime import datetime
        current_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")

        for i in range(1, 21):
            port_number = 50000 + i
            score = i * 10
            ad_count = i * 2
            # Format score with 2 decimal places
            score_display = f"{score:.2f}"
            self.scan_tree.insert('', 'end', values=(i, port_number, score_display, ad_count, current_time))

    def clear_scan_treeview(self):
        """Clear data from ports but keep the ports themselves"""
        # Ask for confirmation
        if messagebox.askyesno("تأیید پاکسازی", "آیا از پاکسازی اطلاعات امتیازها و تبلیغات اطمینان دارید؟\n(پورت‌ها حذف نمی‌شوند، فقط اطلاعات آن‌ها پاک می‌شود)"):
            try:
                # Get the default group
                default_group = self.get_default_group()

                # Reset score, ad_count, previous_score, previous_ad_count for all ports in default group
                self.cursor.execute("""
                    UPDATE ports
                    SET score = 0,
                        ad_count = 0,
                        previous_score = 0,
                        previous_ad_count = 0,
                        update_date = ?,
                        update_time = ?
                    WHERE group_number = ?
                """, (
                    datetime.now().strftime("%Y/%m/%d"),
                    datetime.now().strftime("%H:%M:%S"),
                    default_group
                ))

                self.conn.commit()

                # Reload the treeview to show updated data
                self.load_default_ports_to_scan_tree()

                # Reset summary labels
                self.total_score = 0
                self.total_dollar = 0.0
                self.update_scan_summary_labels()

                # Show confirmation message
                messagebox.showinfo("اطلاعات", "اطلاعات امتیازها و تبلیغات با موفقیت پاکسازی شد.\nپورت‌ها حفظ شدند.")

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در پاکسازی اطلاعات: {e}")

    def show_scan_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under cursor
        item = self.scan_tree.identify_row(event.y)
        if item:
            # Select the item
            self.scan_tree.selection_set(item)
            # Show the context menu
            self.scan_context_menu.post(event.x_root, event.y_root)

    def open_kkt_for_selected_port(self):
        """Open KKT app for the selected port in treeview"""
        try:
            # Get selected item
            selected_items = self.scan_tree.selection()
            if not selected_items:
                messagebox.showwarning("هشدار", "لطفاً یک پورت را انتخاب کنید.")
                return

            # Get the first selected item
            item = selected_items[0]
            values = self.scan_tree.item(item)['values']

            if len(values) < 2:
                messagebox.showerror("خطا", "اطلاعات پورت نامعتبر است.")
                return

            # Get port display value (could be title or port number)
            port_display = values[1]  # Port number/title is in column 1

            # Get actual port number from display value
            port_number = self.get_port_number_from_display(port_display)
            if not port_number:
                messagebox.showerror("خطا", f"پورت {port_display} یافت نشد.")
                return

            # Get port ID
            self.cursor.execute("SELECT id FROM ports WHERE port_number = ?", (port_number,))
            result = self.cursor.fetchone()
            if not result:
                messagebox.showerror("خطا", f"پورت {port_number} در دیتابیس یافت نشد.")
                return

            port_id = result[0]

            # Show progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("باز کردن KKT")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)
            progress_window.transient(self)
            progress_window.grab_set()

            # Center the dialog
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Add progress label
            progress_label = ttk.Label(progress_window, text=f"در حال باز کردن KKT در پورت {port_number}...")
            progress_label.pack(pady=20)

            # Add progress bar
            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill='x')
            progress_bar.start()

            # Add log text widget
            log_frame = ttk.Frame(progress_window)
            log_frame.pack(fill='both', expand=True, padx=20, pady=10)

            log_text = tk.Text(log_frame, height=4, wrap='word')
            log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=log_text.yview)
            log_text.configure(yscrollcommand=log_scrollbar.set)
            log_text.pack(side='left', fill='both', expand=True)
            log_scrollbar.pack(side='right', fill='y')

            def update_log(message):
                """Update log in the progress window"""
                log_text.insert(tk.END, f"{message}\n")
                log_text.see(tk.END)
                progress_window.update()

            def perform_open_kkt():
                """Perform KKT opening in a separate thread"""
                try:
                    update_log(f"شروع باز کردن KKT برای پورت {port_number}")

                    # Call the open_kkt_app function with enhanced logging
                    result = self.open_kkt_app_with_logging(port_id, port_number, update_log)

                    def show_success():
                        progress_window.destroy()
                        if result:
                            messagebox.showinfo("موفقیت", f"برنامه KKT در پورت {port_number} با موفقیت باز شد.")
                        else:
                            messagebox.showwarning("هشدار", f"برنامه KKT در پورت {port_number} قبلاً باز بود.")

                    self.after(0, show_success)

                except Exception as e:
                    error_message = str(e)
                    def show_error():
                        progress_window.destroy()
                        messagebox.showerror("خطا", f"خطا در باز کردن KKT: {error_message}")

                    self.after(0, show_error)

            # Run in a separate thread
            import threading
            open_thread = threading.Thread(target=perform_open_kkt)
            open_thread.daemon = True
            open_thread.start()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در باز کردن KKT: {e}")

    def detect_focused_app_for_selected_port(self):
        """Detect the currently focused app for the selected port in treeview"""
        try:
            # Get selected item
            selected_items = self.scan_tree.selection()
            if not selected_items:
                messagebox.showwarning("هشدار", "لطفاً یک پورت را انتخاب کنید.")
                return

            # Get the first selected item
            item = selected_items[0]
            values = self.scan_tree.item(item)['values']

            if len(values) < 2:
                messagebox.showerror("خطا", "اطلاعات پورت نامعتبر است.")
                return

            # Get port display value (could be title or port number)
            port_display = values[1]  # Port number/title is in column 1

            # Get actual port number from display value
            port_number = self.get_port_number_from_display(port_display)
            if not port_number:
                messagebox.showerror("خطا", f"پورت {port_display} یافت نشد.")
                return

            # Show progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("تشخیص برنامه فعال")
            progress_window.geometry("500x300")
            progress_window.resizable(False, False)
            progress_window.transient(self)
            progress_window.grab_set()

            # Center the dialog
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Add title label
            title_label = ttk.Label(progress_window, text=f"تشخیص برنامه فعال در پورت {port_number}", font=('Arial', 12, 'bold'))
            title_label.pack(pady=10)

            # Add progress bar
            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill='x')
            progress_bar.start()

            # Add result frame
            result_frame = ttk.LabelFrame(progress_window, text="نتیجه تشخیص", padding=10)
            result_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # App info labels
            app_name_label = ttk.Label(result_frame, text="نام برنامه: در حال تشخیص...", font=('Arial', 10))
            app_name_label.pack(anchor='w', pady=2)

            app_type_label = ttk.Label(result_frame, text="نوع برنامه: در حال تشخیص...", font=('Arial', 10))
            app_type_label.pack(anchor='w', pady=2)

            app_package_label = ttk.Label(result_frame, text="نام پکیج: در حال تشخیص...", font=('Arial', 10))
            app_package_label.pack(anchor='w', pady=2)

            # Add log text widget
            log_frame = ttk.LabelFrame(progress_window, text="جزئیات", padding=5)
            log_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))

            log_text = tk.Text(log_frame, height=6, wrap='word', font=('Consolas', 9))
            log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=log_text.yview)
            log_text.configure(yscrollcommand=log_scrollbar.set)
            log_text.pack(side='left', fill='both', expand=True)
            log_scrollbar.pack(side='right', fill='y')

            # Close button
            close_button = ttk.Button(progress_window, text="بستن", command=progress_window.destroy)
            close_button.pack(pady=10)

            def update_log(message):
                """Update log in the progress window"""
                log_text.insert(tk.END, f"{message}\n")
                log_text.see(tk.END)
                progress_window.update()

            def perform_detection():
                """Perform app detection in a separate thread"""
                try:
                    update_log(f"شروع تشخیص برنامه فعال برای پورت {port_number}")

                    # Call the get_current_focused_app function
                    app_info = self.get_current_focused_app(port_number, update_log)

                    def show_result():
                        progress_bar.stop()

                        # Update result labels
                        app_name_label.config(text=f"نام برنامه: {app_info['name']}")

                        # Set color based on app type
                        type_colors = {
                            'kkt': '#4CAF50',      # Green for KKT
                            'browser': '#2196F3',   # Blue for browsers
                            'launcher': '#FF9800',  # Orange for launcher
                            'system': '#9C27B0',    # Purple for system
                            'game': '#E91E63',      # Pink for games
                            'other': '#607D8B',     # Blue-grey for others
                            'unknown': '#F44336'    # Red for unknown
                        }

                        type_color = type_colors.get(app_info['type'], '#000000')
                        app_type_label.config(text=f"نوع برنامه: {app_info['description']}", foreground=type_color)
                        app_package_label.config(text=f"نام پکیج: {app_info['package']}")

                        # Add status icon based on app type
                        status_icons = {
                            'kkt': '🎯',
                            'browser': '🌐',
                            'launcher': '🏠',
                            'system': '⚙️',
                            'game': '🎮',
                            'other': '📱',
                            'unknown': '❓'
                        }

                        icon = status_icons.get(app_info['type'], '❓')
                        title_label.config(text=f"{icon} برنامه فعال در پورت {port_number}: {app_info['name']}")

                        update_log(f"✅ تشخیص کامل شد: {app_info['description']}")

                    self.after(0, show_result)

                except Exception as e:
                    error_message = str(e)
                    def show_error():
                        progress_bar.stop()
                        app_name_label.config(text="نام برنامه: خطا در تشخیص")
                        app_type_label.config(text="نوع برنامه: نامشخص", foreground='red')
                        app_package_label.config(text="نام پکیج: نامشخص")
                        title_label.config(text=f"❌ خطا در تشخیص برنامه فعال")
                        update_log(f"❌ خطا: {error_message}")

                    self.after(0, show_error)

            # Run in a separate thread
            import threading
            detection_thread = threading.Thread(target=perform_detection)
            detection_thread.daemon = True
            detection_thread.start()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در تشخیص برنامه فعال: {e}")

    def open_wallet_management(self):
        """Open wallet management window"""
        try:
            WalletManagementWindow(self)
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در باز کردن مدیریت والت‌ها: {e}")

    def open_wallet_connection(self):
        """Open wallet connection window"""
        try:
            WalletConnectionWindow(self)
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در باز کردن ارتباط با والت: {e}")

    def setup_settings_tab(self):
        """Set up the Settings tab"""
        # Empty tab - will be filled according to user instructions
        pass

    def init_database(self):
        """Initialize the SQLite database for port management"""
        try:
            # Connect to database (will create if not exists)
            self.conn = sqlite3.connect('ports.db')
            self.cursor = self.conn.cursor()

            # Check if ports table exists
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ports'")
            table_exists = self.cursor.fetchone() is not None

            if not table_exists:
                # Create ports table if it doesn't exist
                self.cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ports (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        port_number INTEGER NOT NULL,
                        group_number INTEGER NOT NULL,
                        is_default INTEGER DEFAULT 0,
                        connection_status TEXT DEFAULT 'نامشخص',
                        score INTEGER DEFAULT 0,
                        ad_count INTEGER DEFAULT 0,
                        update_date TEXT,
                        update_time TEXT,
                        title TEXT,
                        UNIQUE(port_number)
                    )
                ''')
            else:
                # Check if new columns exist and add them if they don't
                try:
                    self.cursor.execute("SELECT connection_status FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add connection_status column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN connection_status TEXT DEFAULT 'نامشخص'")
                    self.conn.commit()
                    messagebox.showinfo("به‌روزرسانی دیتابیس", "ستون وضعیت اتصال به دیتابیس اضافه شد.")

                # Check for score column
                try:
                    self.cursor.execute("SELECT score FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add score column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN score INTEGER DEFAULT 0")
                    self.conn.commit()

                # Check for ad_count column
                try:
                    self.cursor.execute("SELECT ad_count FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add ad_count column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN ad_count INTEGER DEFAULT 0")
                    self.conn.commit()

                # Check for update_date column
                try:
                    self.cursor.execute("SELECT update_date FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add update_date column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN update_date TEXT")
                    self.conn.commit()

                # Check for update_time column
                try:
                    self.cursor.execute("SELECT update_time FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add update_time column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN update_time TEXT")
                    self.conn.commit()
                    messagebox.showinfo("به‌روزرسانی دیتابیس", "ستون‌های جدید به دیتابیس اضافه شدند.")

                # Check for title column
                try:
                    self.cursor.execute("SELECT title FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add title column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN title TEXT")
                    self.conn.commit()
                    messagebox.showinfo("به‌روزرسانی دیتابیس", "ستون عنوان به دیتابیس اضافه شد.")

                # Check for previous_score column
                try:
                    self.cursor.execute("SELECT previous_score FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add previous_score column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN previous_score REAL DEFAULT 0")
                    self.conn.commit()

                # Check for previous_ad_count column
                try:
                    self.cursor.execute("SELECT previous_ad_count FROM ports LIMIT 1")
                except sqlite3.OperationalError:
                    # Add previous_ad_count column to existing table
                    self.cursor.execute("ALTER TABLE ports ADD COLUMN previous_ad_count INTEGER DEFAULT 0")
                    self.conn.commit()
                    messagebox.showinfo("به‌روزرسانی دیتابیس", "ستون‌های مقادیر قبلی به دیتابیس اضافه شدند.")

            # Create settings table for default group
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_name TEXT NOT NULL,
                    setting_value TEXT NOT NULL,
                    UNIQUE(setting_name)
                )
            ''')

            # Check if default_group setting exists, if not insert it
            self.cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'default_group'")
            result = self.cursor.fetchone()
            if not result:
                # Default to group 1 if not set
                self.cursor.execute("INSERT INTO settings (setting_name, setting_value) VALUES ('default_group', '1')")

            # Create daily_records table for storing daily summaries
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    record_date TEXT NOT NULL,
                    group_number INTEGER NOT NULL,
                    total_score REAL DEFAULT 0,
                    total_dollar REAL DEFAULT 0,
                    total_ads INTEGER DEFAULT 0,
                    avg_ads REAL DEFAULT 0,
                    port_count INTEGER DEFAULT 0,
                    UNIQUE(record_date, group_number)
                )
            ''')

            # Create port_history table for storing detailed port history
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS port_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    record_date TEXT NOT NULL,
                    port_id INTEGER NOT NULL,
                    port_number INTEGER NOT NULL,
                    group_number INTEGER NOT NULL,
                    score REAL DEFAULT 0,
                    dollar_value REAL DEFAULT 0,
                    ad_count INTEGER DEFAULT 0,
                    FOREIGN KEY (port_id) REFERENCES ports (id),
                    UNIQUE(record_date, port_id)
                )
            ''')

            # Create last_record_time setting if not exists
            self.cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'last_record_time'")
            result = self.cursor.fetchone()
            if not result:
                # Default to current time
                now = datetime.now()
                self.cursor.execute("INSERT INTO settings (setting_name, setting_value) VALUES ('last_record_time', ?)",
                                   (now.strftime("%Y-%m-%d %H:%M:%S"),))

            # Create stale_data_minutes setting if not exists
            self.cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'stale_data_minutes'")
            result = self.cursor.fetchone()
            if not result:
                # Default to 5 minutes
                self.cursor.execute("INSERT INTO settings (setting_name, setting_value) VALUES ('stale_data_minutes', '5')")

            # Create wallet_addresses table if it doesn't exist
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS wallet_addresses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    address TEXT UNIQUE NOT NULL,
                    label TEXT,
                    created_date TEXT,
                    created_time TEXT,
                    last_used_date TEXT,
                    last_used_time TEXT,
                    usage_count INTEGER DEFAULT 0,
                    notes TEXT,
                    is_active INTEGER DEFAULT 1
                )
            ''')

            self.conn.commit()
        except Exception as e:
            messagebox.showerror("خطای دیتابیس", f"خطا در ایجاد دیتابیس: {e}")

    def setup_settings_tab(self):
        """Set up the Settings tab with port management button and other settings"""
        # Create a frame for settings
        settings_frame = ttk.Frame(self.settings_tab)
        settings_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Create buttons frame
        buttons_frame = ttk.Frame(settings_frame)
        buttons_frame.pack(pady=20, fill='x')

        # Add port management button
        port_btn = ttk.Button(buttons_frame, text="🔧 مدیریت پورت‌ها",
                             command=self.open_port_management)
        port_btn.pack(side='right', padx=10, ipadx=10, ipady=5)

        # Add wallet management button
        wallet_btn = ttk.Button(buttons_frame, text="💰 مدیریت آدرس‌های والت",
                               command=self.open_wallet_management)
        wallet_btn.pack(side='right', padx=10, ipadx=10, ipady=5)

        # Add wallet connection button
        wallet_conn_btn = ttk.Button(buttons_frame, text="🔗 ارتباط با والت",
                                    command=self.open_wallet_connection)
        wallet_conn_btn.pack(side='right', padx=10, ipadx=10, ipady=5)

        # Add separator
        ttk.Separator(settings_frame, orient='horizontal').pack(fill='x', pady=10)

        # Add stale data minutes setting
        stale_frame = ttk.LabelFrame(settings_frame, text="تنظیمات رنگ‌بندی")
        stale_frame.pack(fill='x', padx=20, pady=10)

        # Get current stale data minutes
        stale_minutes = self.get_stale_data_minutes()

        # Create a frame for the stale data minutes setting
        stale_setting_frame = ttk.Frame(stale_frame)
        stale_setting_frame.pack(fill='x', padx=10, pady=10)

        # Add label
        ttk.Label(stale_setting_frame,
                 text="زمان رنگی شدن ردیف‌های بدون تغییر (دقیقه):").pack(side='right', padx=5)

        # Add spinbox for stale data minutes
        stale_var = tk.StringVar(value=str(stale_minutes))
        stale_spinbox = ttk.Spinbox(stale_setting_frame, from_=1, to=60,
                                   textvariable=stale_var, width=5)
        stale_spinbox.pack(side='right', padx=5)

        # Add save button
        def save_stale_minutes():
            try:
                minutes = int(stale_var.get())
                if minutes < 1:
                    messagebox.showwarning("هشدار", "زمان باید حداقل 1 دقیقه باشد.")
                    return

                # Update setting in database
                self.cursor.execute(
                    "UPDATE settings SET setting_value = ? WHERE setting_name = 'stale_data_minutes'",
                    (str(minutes),)
                )
                self.conn.commit()
                messagebox.showinfo("موفقیت", f"زمان رنگی شدن ردیف‌های بدون تغییر به {minutes} دقیقه تغییر یافت.")

                # Update color legend
                self.update_color_legend()

                # Reload treeview to apply new setting
                self.load_default_ports_to_scan_tree()

            except ValueError:
                messagebox.showwarning("هشدار", "لطفاً یک عدد صحیح وارد کنید.")

        save_btn = ttk.Button(stale_setting_frame, text="ذخیره", command=save_stale_minutes)
        save_btn.pack(side='right', padx=10)

        # Add description
        description = "این تنظیم مشخص می‌کند که پس از چند دقیقه بدون تغییر در امتیاز و تعداد تبلیغات، ردیف‌ها با رنگ نارنجی نمایش داده شوند."
        desc_label = ttk.Label(stale_frame, text=description, wraplength=400)
        desc_label.pack(fill='x', padx=10, pady=5)

    def load_default_ports_to_scan_tree(self):
        """Load default ports into the scan treeview"""
        # Clear existing items
        for item in self.scan_tree.get_children():
            self.scan_tree.delete(item)

        try:
            # Get the default group
            default_group = self.get_default_group()

            # Configure tag colors for the treeview
            self.scan_tree.tag_configure('score_decreased', background='#ffcccc')  # Light red
            self.scan_tree.tag_configure('score_unchanged', background='#cce5ff')  # Light blue
            self.scan_tree.tag_configure('stale_data', background='#ffcc99')       # Light orange for stale data

            # Get all ports in the default group including previous values
            # First check if previous_score and previous_ad_count columns exist
            try:
                self.cursor.execute("""
                    SELECT id, port_number, score, ad_count, update_date, update_time, title,
                           previous_score, previous_ad_count
                    FROM ports
                    WHERE group_number = ?
                    ORDER BY id
                """, (default_group,))
            except sqlite3.OperationalError as e:
                if "no such column: previous_score" in str(e):
                    # Columns don't exist, use default values
                    self.cursor.execute("""
                        SELECT id, port_number, score, ad_count, update_date, update_time, title,
                               0 as previous_score, 0 as previous_ad_count
                        FROM ports
                        WHERE group_number = ?
                        ORDER BY id
                    """, (default_group,))
                else:
                    raise e

            ports = self.cursor.fetchall()

            if not ports:
                # No ports found in the default group
                return

            # Add to treeview
            for i, port in enumerate(ports, 1):
                port_values = list(port)

                # If date/time fields are empty, use current date/time
                if not port_values[4] or not port_values[5]:
                    now = datetime.now()
                    port_values[4] = now.strftime("%Y/%m/%d")
                    port_values[5] = now.strftime("%H:%M:%S")

                # Get current and previous values
                score = port_values[2] if port_values[2] is not None else 0
                ad_count = port_values[3] if port_values[3] is not None else 0
                previous_score = port_values[7] if len(port_values) > 7 and port_values[7] is not None else 0
                previous_ad_count = port_values[8] if len(port_values) > 8 and port_values[8] is not None else 0

                # Check if data is stale (X minutes since last update, where X is from settings)
                is_stale = False
                if port_values[4] and port_values[5]:
                    try:
                        # Parse the update date and time
                        update_datetime_str = f"{port_values[4]} {port_values[5]}"
                        update_datetime = datetime.strptime(update_datetime_str, "%Y/%m/%d %H:%M:%S")

                        # Calculate time difference
                        now = datetime.now()
                        time_diff = now - update_datetime

                        # Get stale data minutes from settings
                        stale_minutes = self.get_stale_data_minutes()

                        # Check if more than X minutes have passed
                        is_stale = time_diff.total_seconds() > (stale_minutes * 60)  # Convert minutes to seconds
                    except Exception as e:
                        print(f"Error checking stale data: {e}")

                # Format score with 2 decimal places
                score_display = f"{score:.2f}"

                # Calculate dollar value (10000 points = $1)
                dollar_value = score / 10000
                dollar_display = f"${dollar_value:.2f}"

                # Determine tag based on comparison with previous values
                # We'll use a single tag for each row to avoid conflicts
                tag = None

                # Priority: stale data > decreased score > unchanged score
                if is_stale and score == previous_score and (score > 0):
                    # Data is stale (X minutes) and hasn't changed
                    tag = 'stale_data'
                elif score < previous_score:
                    tag = 'score_decreased'
                elif score == previous_score and score > 0:  # Only mark unchanged if not zero
                    tag = 'score_unchanged'

                # Create tags list (either empty or with one tag)
                tags = [tag] if tag else []

                # Add to totals if this is the first time we're loading
                if not hasattr(self, 'total_score'):
                    self.total_score = 0
                    self.total_dollar = 0.0

                self.total_score = getattr(self, 'total_score', 0) + score
                self.total_dollar = getattr(self, 'total_dollar', 0.0) + dollar_value

                # Format as: row, port_number/title, score, ad_count, update_date + update_time
                update_datetime = f"{port_values[4]} {port_values[5]}"

                # Use title if available, otherwise use port number
                port_title = port_values[6] if len(port_values) > 6 and port_values[6] else port_values[1]

                # Display values in the treeview - match columns defined in setup_scan_treeview
                display_values = (i, port_title, score_display, dollar_display, ad_count, update_datetime)

                # Insert with tags for coloring
                self.scan_tree.insert('', 'end', values=display_values, tags=tags)

        except Exception as e:
            print(f"Error loading default ports to scan tree: {e}")

        # Update summary labels after loading
        if hasattr(self, 'total_score') and hasattr(self, 'total_dollar'):
            self.update_scan_summary_labels(self.total_score, self.total_dollar)
            # Reset totals for next time
            self.total_score = 0
            self.total_dollar = 0.0

    def update_color_legend(self):
        """Update the color legend with current stale minutes setting"""
        # Clear existing color legends
        for widget in self.color_legends_frame.winfo_children():
            widget.destroy()

        # Get stale minutes from settings
        stale_minutes = self.get_stale_data_minutes()

        # Create color samples with labels
        colors = [
            ("#ffcccc", "کاهش امتیاز"),
            ("#cce5ff", "بدون تغییر"),
            ("#ffcc99", f"بدون تغییر بیش از {stale_minutes} دقیقه")
        ]

        for i, (color, text) in enumerate(colors):
            # Create a frame for each color
            color_frame = ttk.Frame(self.color_legends_frame)
            color_frame.pack(side='right', padx=10)

            # Create a colored square
            color_sample = tk.Canvas(color_frame, width=15, height=15, bg=color, highlightthickness=1, highlightbackground="black")
            color_sample.pack(side='right', padx=2)

            # Create a label
            ttk.Label(color_frame, text=text).pack(side='right')

    def update_scan_summary_labels(self, total_score=None, total_dollar=None):
        """Update summary labels with total score and dollar value"""
        # If total_score and total_dollar are not provided, calculate them from the treeview
        if total_score is None or total_dollar is None:
            total_score = 0
            total_dollar = 0.0

            # Get all items from the treeview
            for item_id in self.scan_tree.get_children():
                item = self.scan_tree.item(item_id)
                values = item['values']

                # Check if we have enough values and the score is at index 2
                if len(values) > 2:
                    try:
                        # Score is at index 2, remove commas and convert to float
                        score_str = str(values[2]).replace(',', '')
                        score = float(score_str)
                        total_score += score

                        # Dollar value is at index 3, remove $ and commas and convert to float
                        if len(values) > 3:
                            dollar_str = str(values[3]).replace('$', '').replace(',', '')
                            dollar = float(dollar_str)
                            total_dollar += dollar
                    except (ValueError, TypeError):
                        # Skip items with invalid values
                        pass

        # Format total score with commas and 2 decimal places
        formatted_score = f"{total_score:,.2f}"
        self.total_score_label.config(text=f"مجموع امتیازها: {formatted_score}")

        # Format dollar value with 2 decimal places
        self.total_dollar_label.config(text=f"ارزش کل به دلار: ${total_dollar:.2f}")

    def setup_auto_scan(self):
        """Setup automatic scanning with interval selection"""
        if self.auto_scan_active:
            # If auto scan is already active, cancel it
            self.cancel_auto_scan()
            return

        # Create a dialog to get the interval
        auto_scan_dialog = tk.Toplevel(self)
        auto_scan_dialog.title("تنظیم اسکن اتوماتیک")
        auto_scan_dialog.geometry("300x200")
        auto_scan_dialog.resizable(False, False)
        auto_scan_dialog.transient(self)  # Set to be on top of the main window
        auto_scan_dialog.grab_set()  # Modal dialog

        # Center the dialog
        auto_scan_dialog.update_idletasks()
        width = auto_scan_dialog.winfo_width()
        height = auto_scan_dialog.winfo_height()
        x = (auto_scan_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (auto_scan_dialog.winfo_screenheight() // 2) - (height // 2)
        auto_scan_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Create a frame for the content
        content_frame = ttk.Frame(auto_scan_dialog, padding=10)
        content_frame.pack(fill='both', expand=True)

        # Add a label
        ttk.Label(content_frame, text="فاصله زمانی اسکن اتوماتیک (دقیقه):", anchor='center').pack(pady=10)

        # Add a combobox for interval selection
        interval_var = tk.StringVar()
        interval_combo = ttk.Combobox(content_frame, textvariable=interval_var, width=10)
        interval_combo['values'] = ('1', '2', '3', '5', '10', '15', '30', '60')
        interval_combo.current(1)  # Default to 2 minutes
        interval_combo.pack(pady=10)

        # Add buttons
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(pady=20, fill='x')

        # Start button
        def start_auto_scan():
            try:
                interval = int(interval_var.get())
                if interval < 1:
                    messagebox.showwarning("هشدار", "فاصله زمانی باید حداقل 1 دقیقه باشد.")
                    return

                auto_scan_dialog.destroy()
                self.start_auto_scan(interval)
            except ValueError:
                messagebox.showwarning("هشدار", "لطفاً یک عدد صحیح وارد کنید.")

        ttk.Button(button_frame, text="شروع", command=start_auto_scan).pack(side='right', padx=5)

        # Cancel button
        ttk.Button(button_frame, text="انصراف", command=auto_scan_dialog.destroy).pack(side='left', padx=5)

    def start_auto_scan(self, interval_minutes):
        """Start automatic scanning with the specified interval"""
        # Convert minutes to milliseconds
        interval_ms = interval_minutes * 60 * 1000

        # Update button text and style
        self.btn_auto_scan.config(text="⏹️ توقف اسکن اتوماتیک")
        self.style.configure("AutoActive.TButton", background="orange")
        self.btn_auto_scan.config(style="AutoActive.TButton")

        # Set auto scan as active
        self.auto_scan_active = True

        # Show a message
        messagebox.showinfo("اسکن اتوماتیک", f"اسکن اتوماتیک هر {interval_minutes} دقیقه فعال شد.")

        # Store interval for countdown
        self.auto_scan_interval = interval_minutes

        # Define the auto scan function
        def do_auto_scan():
            if self.auto_scan_active:
                # Check if a scan is already running
                if not hasattr(self, 'scan_running') or not self.scan_running:
                    # Start a manual scan
                    self.start_manual_scan()

                    # Note: The update_daily_records() is already called inside start_manual_scan()
                    # so we don't need to call it again here

                # Start countdown for next scan
                self.start_countdown(interval_minutes * 60, "اتوماتیک")
                # Schedule the next scan
                self.auto_scan_job = self.after(interval_ms, do_auto_scan)

        # Start the first scan immediately
        do_auto_scan()

    def cancel_auto_scan(self):
        """Cancel automatic scanning"""
        if self.auto_scan_active:
            # Cancel the scheduled job
            if self.auto_scan_job:
                self.after_cancel(self.auto_scan_job)
                self.auto_scan_job = None

            # Update button text and style
            self.btn_auto_scan.config(text="⏱️ اسکن اتوماتیک", style="TButton")

            # Set auto scan as inactive
            self.auto_scan_active = False

            # Clear countdown
            self.countdown_label.config(text="")
            if self.countdown_job:
                self.after_cancel(self.countdown_job)
                self.countdown_job = None

            # Show a message
            messagebox.showinfo("اسکن اتوماتیک", "اسکن اتوماتیک متوقف شد.")

    def setup_robotic_scan(self):
        """Setup robotic scanning with interval selection"""
        # Create a dialog to get the interval
        robotic_scan_dialog = tk.Toplevel(self)
        robotic_scan_dialog.title("تنظیم اسکن رباتیک")
        robotic_scan_dialog.geometry("800x600")  # Much larger size
        robotic_scan_dialog.resizable(True, True)  # Allow resizing
        robotic_scan_dialog.transient(self)  # Set to be on top of the main window
        robotic_scan_dialog.grab_set()  # Modal dialog

        # Center the dialog
        robotic_scan_dialog.update_idletasks()
        width = robotic_scan_dialog.winfo_width()
        height = robotic_scan_dialog.winfo_height()
        x = (robotic_scan_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (robotic_scan_dialog.winfo_screenheight() // 2) - (height // 2)
        robotic_scan_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Create a frame for the content
        content_frame = ttk.Frame(robotic_scan_dialog, padding=20)
        content_frame.pack(fill='both', expand=True)

        # Add a label with larger font
        title_label = ttk.Label(content_frame, text="فاصله زمانی اسکن رباتیک (ثانیه):", anchor='center')
        title_label.configure(font=('Arial', 12, 'bold'))
        title_label.pack(pady=15)

        # Add a combobox for interval selection with larger size
        interval_var = tk.StringVar()
        interval_combo = ttk.Combobox(content_frame, textvariable=interval_var, width=15, font=('Arial', 11))
        interval_combo['values'] = ('10', '15', '20', '30', '45', '60', '90', '120', '180', '300')
        interval_combo.current(2)  # Default to 20 seconds
        interval_combo.pack(pady=15)

        # Add a frame for checkboxes with larger size
        checkbox_frame = ttk.LabelFrame(content_frame, text="گزینه‌های اضافی", padding=10)
        checkbox_frame.pack(fill='both', expand=True, pady=15, padx=10)

        # Create a scrollable frame for checkboxes with mouse wheel support
        checkbox_canvas = tk.Canvas(checkbox_frame, height=300)  # Larger height
        checkbox_canvas.pack(side='left', fill='both', expand=True)

        scrollbar = ttk.Scrollbar(checkbox_frame, orient='vertical', command=checkbox_canvas.yview)
        scrollbar.pack(side='right', fill='y')

        checkbox_canvas.configure(yscrollcommand=scrollbar.set)
        checkbox_canvas.bind('<Configure>', lambda e: checkbox_canvas.configure(scrollregion=checkbox_canvas.bbox('all')))

        # Add mouse wheel scrolling support
        def _on_mousewheel(event):
            checkbox_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            checkbox_canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            checkbox_canvas.unbind_all("<MouseWheel>")

        checkbox_canvas.bind('<Enter>', _bind_to_mousewheel)
        checkbox_canvas.bind('<Leave>', _unbind_from_mousewheel)

        checkbox_inner_frame = ttk.Frame(checkbox_canvas)
        checkbox_canvas.create_window((0, 0), window=checkbox_inner_frame, anchor='nw')

        # Add checkboxes in the requested order with numbering

        # 1. باز کردن KKT
        open_kkt_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="1️⃣ باز کردن KKT (com.KepithorStudios.KKTFaucet)",
                       variable=open_kkt_var).pack(anchor='w', padx=10, pady=5)

        # 2. کلیک روی Enter
        enter_button_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="2️⃣ کلیک روی Enter - تشخیص دکمه Enter و کلیک روی آن",
                       variable=enter_button_var).pack(anchor='w', padx=10, pady=5)

        # 3. تشخیص سرور
        detect_server_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="3️⃣ تشخیص سرور - کلیک خودکار روی سرور سبز/زرد",
                       variable=detect_server_var).pack(anchor='w', padx=10, pady=5)

        # 4. مجوز تبلیغات
        privacy_consent_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="4️⃣ مجوز تبلیغات - تشخیص Privacy Information و کلیک Save Settings",
                       variable=privacy_consent_var).pack(anchor='w', padx=10, pady=5)

        # 5. تشخیص Loading
        close_loading_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="5️⃣ تشخیص Loading - اگر صفحه لودینگ تشخیص داده شد منتظر بماند",
                       variable=close_loading_var).pack(anchor='w', padx=10, pady=5)

        # 6. تشخیص صفحه مشکی
        black_screen_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="6️⃣ تشخیص صفحه مشکی - اگر صفحه مشکی/خاکستری تشخیص داده شد گزارش دهد",
                       variable=black_screen_var).pack(anchor='w', padx=10, pady=5)

        # 7. پخش تبلیغات
        play_ads_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="7️⃣ پخش تبلیغات - تشخیص ایکن بلندگو و کلیک برای پخش تبلیغ",
                       variable=play_ads_var).pack(anchor='w', padx=10, pady=5)

        # 8. بستن KKT پس از اسکن
        close_kkt_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_inner_frame, text="8️⃣ بستن KKT پس از اسکن",
                       variable=close_kkt_var).pack(anchor='w', padx=10, pady=5)

        # Add buttons with improved styling
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(pady=20, fill='x')

        # Start button
        def start_robotic_scan():
            try:
                interval = int(interval_var.get())
                if interval < 10:
                    messagebox.showwarning("هشدار", "فاصله زمانی باید حداقل 10 ثانیه باشد.")
                    return

                # Get checkbox values
                options = {
                    'open_kkt': open_kkt_var.get(),
                    'close_kkt': close_kkt_var.get(),
                    'detect_server': detect_server_var.get(),
                    'close_loading': close_loading_var.get(),
                    'privacy_consent': privacy_consent_var.get(),
                    'black_screen': black_screen_var.get(),
                    'enter_button': enter_button_var.get(),
                    'play_ads': play_ads_var.get()
                }

                robotic_scan_dialog.destroy()
                self.start_robotic_scan(interval, options)
            except ValueError:
                messagebox.showwarning("هشدار", "لطفاً یک عدد صحیح وارد کنید.")

        # Create buttons with larger size
        start_button = ttk.Button(button_frame, text="شروع اسکن رباتیک", command=start_robotic_scan, width=20)
        start_button.pack(side='right', padx=10)

        # Cancel button
        cancel_button = ttk.Button(button_frame, text="انصراف", command=robotic_scan_dialog.destroy, width=10)
        cancel_button.pack(side='left', padx=10)

        # Add a separator for visual clarity
        ttk.Separator(content_frame, orient='horizontal').pack(fill='x', pady=5, before=button_frame)

    def start_robotic_scan(self, interval_seconds, options=None):
        """Start robotic scanning with the specified interval and options (like automatic scan)"""
        # Set default options if none provided
        if options is None:
            options = {'open_kkt': False, 'close_kkt': False}

        # Check if robotic scan is already running
        if hasattr(self, 'robotic_scan_active') and self.robotic_scan_active:
            messagebox.showwarning("هشدار", "اسکن رباتیک در حال اجراست.")
            return

        # Convert seconds to milliseconds
        interval_ms = interval_seconds * 1000

        # Show a message with numbered options
        message = f"اسکن رباتیک هر {interval_seconds} ثانیه فعال شد.\n\nگزینه‌های فعال:"

        if options.get('open_kkt', False):
            message += "\n1️⃣ باز کردن KKT"
        if options.get('enter_button', False):
            message += "\n2️⃣ کلیک روی Enter"
        if options.get('detect_server', False):
            message += "\n3️⃣ تشخیص سرور"
        if options.get('privacy_consent', False):
            message += "\n4️⃣ مجوز تبلیغات"
        if options.get('close_loading', False):
            message += "\n5️⃣ بستن Loading"
        if options.get('black_screen', False):
            message += "\n6️⃣ تشخیص صفحه مشکی"
        if options.get('play_ads', False):
            message += "\n7️⃣ پخش تبلیغات"
        if options.get('close_kkt', False):
            message += "\n8️⃣ بستن KKT پس از اسکن"

        # If no options are selected
        if not any(options.values()):
            message += "\n❌ هیچ گزینه‌ای انتخاب نشده - فقط اسکرین‌شات گرفته می‌شود"

        messagebox.showinfo("اسکن رباتیک", message)

        # Flag to track if robotic scan is active
        self.robotic_scan_active = True

        # Store the interval for potential restart
        self.robotic_scan_interval = interval_seconds

        # Store the job ID for cancellation
        self.robotic_scan_job = None

        # Store options for use in the scan function
        self.robotic_scan_options = options

        # Update button text and style
        self.btn_robotic_scan.config(text="⏹️ توقف اسکن رباتیک", command=self.stop_robotic_scan)
        self.style.configure("RoboticActive.TButton", background="red")
        self.btn_robotic_scan.config(style="RoboticActive.TButton")

        # Update status
        self.scan_status_label.config(text="وضعیت: اسکن رباتیک فعال - در انتظار اسکن بعدی...")

        # Define the robotic scan function (runs in thread like automatic scan)
        def do_robotic_scan():
            if not self.robotic_scan_active:
                return

            # Update status
            self.scan_status_label.config(text="وضعیت: در حال اسکن رباتیک...")
            self.scan_progress_var.set(0)
            self.scan_progress_bar.grid()

            try:
                # Get default group
                default_group = self.get_default_group()

                # Get all ports in the default group using thread-safe connection
                conn = sqlite3.connect('ports.db')
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, port_number FROM ports
                    WHERE group_number = ?
                    ORDER BY id
                """, (default_group,))

                ports = cursor.fetchall()
                conn.close()

                if not ports:
                    print(f"هیچ پورتی در گروه {default_group} یافت نشد.")
                    return

                # Take screenshots for each port
                total_ports = len(ports)
                for i, (port_id, port_number) in enumerate(ports):
                    if not self.robotic_scan_active:
                        break

                    # Update progress
                    progress_percent = int((i / total_ports) * 100)
                    self.scan_progress_var.set(progress_percent)
                    self.scan_status_label.config(text=f"وضعیت: اسکن رباتیک پورت {port_number} ({i+1} از {total_ports})")
                    print(f"اسکن رباتیک پورت {port_number}...")

                    try:
                        print(f"🔍 تمام گزینه‌های اسکن رباتیک: {self.robotic_scan_options}")

                        # 1️⃣ باز کردن KKT
                        if self.robotic_scan_options.get('open_kkt', False):
                            try:
                                print(f"1️⃣ شروع باز کردن KKT برای پورت {port_number}...")
                                result = self.open_kkt_app_with_logging(port_id, port_number)
                                if result:
                                    print(f"✅ برنامه KKT در پورت {port_number} با موفقیت باز شد.")
                                else:
                                    print(f"ℹ️ برنامه KKT در پورت {port_number} قبلاً باز بود.")
                            except Exception as e:
                                print(f"❌ خطا در باز کردن KKT در پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 1️⃣ باز کردن KKT برای پورت {port_number} غیرفعال است")

                        # Take screenshot for analysis
                        screenshot_path = self.take_port_screenshot(port_id, port_number)
                        print(f"📸 اسکرین‌شات پورت {port_number} با موفقیت گرفته شد.")

                        # 🔍 تشخیص برنامه فعال
                        try:
                            print(f"🔍 تشخیص برنامه فعال در پورت {port_number}...")
                            current_app = self.get_current_focused_app(port_number)
                            app_type = current_app['type']
                            app_name = current_app['name']
                            print(f"📱 برنامه فعال: {current_app['description']} ({app_name})")

                            # Store current app info for decision making
                            current_app_info = current_app
                        except Exception as e:
                            print(f"❌ خطا در تشخیص برنامه فعال: {e}")
                            current_app_info = {"type": "unknown", "name": "نامشخص", "description": "نامشخص"}

                        # 2️⃣ کلیک روی Enter
                        if self.robotic_scan_options.get('enter_button', False):
                            try:
                                print(f"2️⃣ تشخیص دکمه Enter برای پورت {port_number}...")
                                if self.detect_and_click_enter_button(port_id, port_number, screenshot_path):
                                    print(f"✅ دکمه Enter در پورت {port_number} تشخیص داده شد و کلیک شد.")
                                else:
                                    print(f"ℹ️ دکمه Enter در پورت {port_number} تشخیص داده نشد.")
                            except Exception as e:
                                print(f"❌ خطا در تشخیص دکمه Enter برای پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 2️⃣ کلیک روی Enter برای پورت {port_number} غیرفعال است")

                        # 3️⃣ تشخیص سرور
                        if self.robotic_scan_options.get('detect_server', False):
                            try:
                                print(f"3️⃣ تشخیص سرور برای پورت {port_number}...")
                                self.detect_and_click_server(port_id, port_number, screenshot_path)
                                print(f"✅ تشخیص سرور برای پورت {port_number} انجام شد.")
                            except Exception as e:
                                print(f"❌ خطا در تشخیص سرور برای پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 3️⃣ تشخیص سرور برای پورت {port_number} غیرفعال است")

                        # 4️⃣ مجوز تبلیغات
                        if self.robotic_scan_options.get('privacy_consent', False):
                            try:
                                print(f"4️⃣ تشخیص مجوز تبلیغات برای پورت {port_number}...")
                                if self.detect_and_click_privacy_consent(port_id, port_number, screenshot_path):
                                    print(f"✅ صفحه Privacy Information در پورت {port_number} تشخیص داده شد و Save Settings کلیک شد.")
                                else:
                                    print(f"ℹ️ صفحه Privacy Information در پورت {port_number} تشخیص داده نشد.")
                            except Exception as e:
                                print(f"❌ خطا در تشخیص Privacy Information برای پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 4️⃣ مجوز تبلیغات برای پورت {port_number} غیرفعال است")

                        # 5️⃣ بستن Loading
                        if self.robotic_scan_options.get('close_loading', False):
                            try:
                                print(f"5️⃣ تشخیص Loading برای پورت {port_number}...")
                                if self.detect_loading_screen(screenshot_path):
                                    print(f"✅ صفحه Loading در پورت {port_number} تشخیص داده شد - منتظر می‌مانیم...")
                                    # Instead of closing KKT, just wait for loading to finish
                                    time.sleep(3)
                                    print(f"⏳ انتظار 3 ثانیه برای پایان Loading...")
                                else:
                                    print(f"ℹ️ صفحه Loading در پورت {port_number} تشخیص داده نشد.")
                            except Exception as e:
                                print(f"❌ خطا در تشخیص Loading برای پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 5️⃣ بستن Loading برای پورت {port_number} غیرفعال است")

                        # 6️⃣ تشخیص صفحه مشکی
                        if self.robotic_scan_options.get('black_screen', False):
                            try:
                                print(f"6️⃣ تشخیص صفحه مشکی برای پورت {port_number}...")
                                if self.detect_black_screen(screenshot_path):
                                    print(f"⚠️ صفحه مشکی در پورت {port_number} تشخیص داده شد - نیاز به بررسی دستی")
                                    # Don't automatically close KKT, just log it for manual review
                                    print(f"💡 توصیه: بررسی دستی پورت {port_number} برای رفع مشکل صفحه مشکی")
                                else:
                                    print(f"✅ صفحه مشکی در پورت {port_number} تشخیص داده نشد.")
                            except Exception as e:
                                print(f"❌ خطا در تشخیص صفحه مشکی برای پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 6️⃣ تشخیص صفحه مشکی برای پورت {port_number} غیرفعال است")

                        # 7️⃣ پخش تبلیغات
                        if self.robotic_scan_options.get('play_ads', False):
                            try:
                                print(f"7️⃣ تشخیص ایکن بلندگو برای پورت {port_number}...")
                                if self.detect_and_click_ad_speaker(port_id, port_number, screenshot_path):
                                    print(f"✅ ایکن بلندگو در پورت {port_number} تشخیص داده شد و کلیک شد.")
                                else:
                                    print(f"ℹ️ ایکن بلندگو در پورت {port_number} تشخیص داده نشد.")
                            except Exception as e:
                                print(f"❌ خطا در تشخیص ایکن بلندگو برای پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 7️⃣ پخش تبلیغات برای پورت {port_number} غیرفعال است")

                        # 8️⃣ بستن KKT پس از اسکن
                        if self.robotic_scan_options.get('close_kkt', False):
                            try:
                                print(f"8️⃣ بستن KKT برای پورت {port_number}...")
                                self.close_kkt_app_for_port(port_id, port_number)
                                print(f"✅ برنامه KKT در پورت {port_number} بسته شد.")
                            except Exception as e:
                                print(f"❌ خطا در بستن KKT در پورت {port_number}: {e}")
                        else:
                            print(f"⏭️ 8️⃣ بستن KKT برای پورت {port_number} غیرفعال است")

                    except Exception as e:
                        print(f"❌ خطا در اسکن پورت {port_number}: {e}")

                # Update progress to 100%
                self.scan_progress_var.set(100)
                self.scan_status_label.config(text="وضعیت: اسکن رباتیک با موفقیت انجام شد")
                print("اسکن رباتیک با موفقیت انجام شد.")

                # Hide progress bar after a short delay
                self.after(2000, lambda: self.scan_progress_bar.grid_remove())

                # Schedule next scan
                if self.robotic_scan_active:
                    print(f"اسکن رباتیک بعدی در {interval_seconds} ثانیه دیگر...")
                    self.scan_status_label.config(text="وضعیت: اسکن رباتیک فعال - در انتظار اسکن بعدی...")
                    # Start countdown for next scan
                    self.start_countdown(interval_seconds, "رباتیک")
                    self.robotic_scan_job = self.after(interval_ms, lambda: threading.Thread(target=do_robotic_scan, daemon=True).start())

            except Exception as e:
                print(f"خطا در اسکن رباتیک: {e}")
                self.scan_status_label.config(text="وضعیت: خطا در اسکن رباتیک")

                # Schedule next scan despite error
                if self.robotic_scan_active:
                    print(f"اسکن رباتیک بعدی در {interval_seconds} ثانیه دیگر...")
                    # Start countdown for next scan
                    self.start_countdown(interval_seconds, "رباتیک")
                    self.robotic_scan_job = self.after(interval_ms, lambda: threading.Thread(target=do_robotic_scan, daemon=True).start())

        # Start the first scan in a thread
        threading.Thread(target=do_robotic_scan, daemon=True).start()



    def take_port_screenshot(self, port_id, port_number):
        """Take a screenshot of a port without OCR processing using adb"""
        # Create screenshots directory if it doesn't exist
        if not os.path.exists(SCREENSHOT_DIR):
            os.makedirs(SCREENSHOT_DIR)

        try:
            # Try to use adb directly
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists in the specified path
            if not os.path.exists(adb_path):
                # Try to find adb in PATH
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    # If we get here, adb is in PATH
                    adb_path = "adb"
                    print(f"Using adb from PATH for screenshot")
                except (subprocess.SubprocessError, FileNotFoundError):
                    raise Exception("adb.exe یافت نشد. لطفاً مطمئن شوید که adb در مسیر برنامه یا PATH وجود دارد.")

            # Check if device is connected
            print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
            devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
            print(f"Connected devices: {devices_result.stdout}")

            if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                # Try to connect to the device
                print(f"Trying to connect to 127.0.0.1:{port_number} for screenshot...")
                connect_cmd = f'{adb_path} connect 127.0.0.1:{port_number}'
                connect_result = subprocess.run(connect_cmd, shell=True, capture_output=True, text=True)
                print(f"Connect result: {connect_result.stdout}")

                # Check again if connected
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices after connect attempt: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"اتصال به دستگاه با پورت {port_number} امکان‌پذیر نیست.")

            # Create screenshot path
            screenshot_path = os.path.join(SCREENSHOT_DIR, f"port_{port_number}.png")

            # Take screenshot using adb
            print(f"Taking screenshot for port {port_number}...")
            screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
            screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
            print(f"Screencap result: {screencap_result.stdout}")

            # Pull screenshot from device
            pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png "{screenshot_path}"'
            pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
            print(f"Pull result: {pull_result.stdout}")

            # Check if screenshot was taken
            if os.path.exists(screenshot_path):
                print(f"Screenshot saved to {screenshot_path}")
                return screenshot_path
            else:
                raise Exception(f"اسکرین‌شات ذخیره نشد.")

        except Exception as e:
            print(f"Error in take_port_screenshot: {e}")
            raise Exception(f"خطا در گرفتن اسکرین‌شات: {e}")

    def detect_and_click_server(self, port_id, port_number, screenshot_path):
        """Advanced server detection that handles various color combinations"""
        try:
            from PIL import Image
            import numpy as np
            import pytesseract

            # Load the screenshot
            image = Image.open(screenshot_path)

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Convert to numpy array for color analysis
            img_array = np.array(image)
            height, width = img_array.shape[:2]

            print(f"تحلیل تصویر با ابعاد: {width}x{height}")

            # STEP 1: Enhanced server screen detection with multiple methods
            print("🔍 مرحله 1: تشخیص صفحه انتخاب سرور...")

            # Method 1: OCR detection for "Select Server" text
            title_area = image.crop((0, 0, width, height // 3))  # Top third of image
            server_screen_detected = False
            detection_method = ""

            try:
                # Use multiple OCR configurations for better detection
                ocr_configs = [
                    r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
                    r'--oem 3 --psm 7',
                    r'--oem 3 --psm 8',
                    r'--oem 1 --psm 6'
                ]

                for i, config in enumerate(ocr_configs):
                    try:
                        ocr_text = pytesseract.image_to_string(title_area, config=config).strip().lower()
                        print(f"OCR تلاش {i+1}: '{ocr_text}'")

                        # Check for various server-related keywords
                        server_keywords = ["select", "server", "choose", "pick", "region", "world"]
                        keyword_found = any(keyword in ocr_text for keyword in server_keywords)

                        if keyword_found:
                            print(f"✅ کلمه کلیدی سرور تشخیص داده شد در تلاش {i+1}")
                            server_screen_detected = True
                            detection_method = f"OCR method {i+1}"
                            break
                    except:
                        continue

            except Exception as ocr_error:
                print(f"خطا در OCR: {ocr_error}")

            # Method 2: Visual pattern detection if OCR fails
            if not server_screen_detected:
                print("🎨 روش 2: تشخیص بصری الگوهای صفحه سرور...")

                # Check for blue dialog/popup background
                blue_ranges = [
                    ([30, 120, 200], [120, 180, 255]),  # Standard blue
                    ([50, 100, 180], [150, 200, 255]),  # Light blue
                    ([20, 80, 160], [100, 160, 240])    # Dark blue
                ]

                total_blue_pixels = 0
                for blue_lower, blue_upper in blue_ranges:
                    blue_lower = np.array(blue_lower)
                    blue_upper = np.array(blue_upper)
                    blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)
                    total_blue_pixels += np.sum(blue_mask)

                print(f"مجموع پیکسل‌های آبی: {total_blue_pixels}")

                # Check for server button colors (green, orange, yellow, red)
                server_color_pixels = 0
                server_color_ranges = [
                    ([0, 150, 0], [100, 255, 100]),      # Green
                    ([200, 100, 0], [255, 180, 80]),     # Orange
                    ([200, 200, 0], [255, 255, 100]),    # Yellow
                    ([150, 0, 0], [255, 80, 80])         # Red
                ]

                for color_lower, color_upper in server_color_ranges:
                    color_lower = np.array(color_lower)
                    color_upper = np.array(color_upper)
                    color_mask = np.all((img_array >= color_lower) & (img_array <= color_upper), axis=2)
                    server_color_pixels += np.sum(color_mask)

                print(f"مجموع پیکسل‌های رنگی سرور: {server_color_pixels}")

                # Check for grid layout pattern (multiple rectangular regions)
                grid_score = self.detect_grid_pattern(img_array)
                print(f"امتیاز الگوی شبکه‌ای: {grid_score}")

                # Server screen detection criteria
                if (total_blue_pixels > 300 and server_color_pixels > 200) or grid_score > 3:
                    server_screen_detected = True
                    detection_method = "Visual pattern detection"
                    print("✅ صفحه سرور از طریق تشخیص بصری تأیید شد")

            # Method 3: Layout-based detection
            if not server_screen_detected:
                print("📐 روش 3: تشخیص بر اساس چیدمان صفحه...")

                # Check for typical server selection layout
                # - Multiple horizontal bands (server rows)
                # - Consistent spacing
                # - Button-like regions

                layout_score = self.analyze_server_layout(img_array)
                print(f"امتیاز چیدمان سرور: {layout_score}")

                if layout_score > 2:
                    server_screen_detected = True
                    detection_method = "Layout analysis"
                    print("✅ صفحه سرور از طریق تحلیل چیدمان تأیید شد")

            if not server_screen_detected:
                print("❌ صفحه انتخاب سرور تشخیص داده نشد")
                return False

            print(f"🎯 صفحه انتخاب سرور تأیید شد (روش: {detection_method})")

            # STEP 2: If we reach here, this is likely a server selection screen
            print("🎯 مرحله 2: تشخیص و انتخاب بهترین سرور...")

            print("صفحه انتخاب سرور تأیید شد")

            # Define precise server button positions based on the layout shown in images
            # 10 servers arranged in 2 columns, 5 rows
            # Based on the images: dialog is centered, buttons are evenly spaced

            # Calculate dialog area (the blue popup area)
            dialog_left = int(width * 0.05)    # Dialog starts at ~5% from left
            dialog_right = int(width * 0.95)   # Dialog ends at ~95% from left
            dialog_top = int(height * 0.25)    # Dialog starts at ~25% from top
            dialog_bottom = int(height * 0.85) # Dialog ends at ~85% from top

            dialog_width = dialog_right - dialog_left
            dialog_height = dialog_bottom - dialog_top

            print(f"ناحیه دیالوگ: ({dialog_left}, {dialog_top}) تا ({dialog_right}, {dialog_bottom})")

            # Server button layout within dialog
            # First button starts after title area, buttons are evenly distributed
            buttons_start_y = dialog_top + int(dialog_height * 0.25)  # After title
            buttons_height = int(dialog_height * 0.65)  # Available height for buttons

            button_width = int(dialog_width * 0.4)   # Each button is ~40% of dialog width
            button_height = int(buttons_height / 5)  # 5 rows of buttons

            # Column positions
            col1_x = dialog_left + int(dialog_width * 0.1)   # Left column at 10% of dialog
            col2_x = dialog_left + int(dialog_width * 0.55)  # Right column at 55% of dialog

            print(f"تنظیمات دکمه‌ها: عرض={button_width}, ارتفاع={button_height}")
            print(f"ستون 1: x={col1_x}, ستون 2: x={col2_x}")

            # Define server button positions (Server 1-10)
            server_positions = []
            for row in range(5):  # 5 rows
                y = buttons_start_y + row * button_height
                # Server numbers: odd numbers in left column, even in right column
                # Row 0: Server 1 (left), Server 6 (right)
                # Row 1: Server 2 (left), Server 7 (right)
                # etc.
                server_positions.append({
                    'server': row + 1,
                    'x': col1_x,
                    'y': y,
                    'width': button_width,
                    'height': button_height
                })
                server_positions.append({
                    'server': row + 6,
                    'x': col2_x,
                    'y': y,
                    'width': button_width,
                    'height': button_height
                })

            # Define color ranges for server status icons
            color_ranges = {
                'green': ([40, 120, 40], [100, 255, 100]),      # Green (usable)
                'yellow': ([120, 180, 40], [180, 255, 120]),    # Yellow (usable)
                'orange': ([150, 120, 0], [255, 200, 80]),      # Orange/Yellow (usable)
                'red': ([120, 40, 40], [255, 100, 100]),        # Red (not usable)
            }

            # Analyze each server button for status icon color
            server_candidates = []

            print(f"🔍 بررسی {len(server_positions)} دکمه سرور...")

            for server_info in server_positions:
                server_num = server_info['server']
                x = server_info['x']
                y = server_info['y']
                btn_width = server_info['width']
                btn_height = server_info['height']

                # Focus on the icon area (left side of button where the colored icon is)
                # Icon is typically in the first 20% of button width
                icon_width = int(btn_width * 0.2)
                icon_height = int(btn_height * 0.6)  # Icon is in middle 60% of button height
                icon_y_offset = int(btn_height * 0.2)  # Start 20% down from button top

                icon_x_start = x + 10  # Small margin from button edge
                icon_y_start = y + icon_y_offset
                icon_x_end = icon_x_start + icon_width
                icon_y_end = icon_y_start + icon_height

                # Ensure we don't go out of bounds
                icon_x_start = max(0, min(icon_x_start, width-1))
                icon_y_start = max(0, min(icon_y_start, height-1))
                icon_x_end = max(icon_x_start+1, min(icon_x_end, width))
                icon_y_end = max(icon_y_start+1, min(icon_y_end, height))

                # Extract icon region
                icon_region = img_array[icon_y_start:icon_y_end, icon_x_start:icon_x_end]

                if icon_region.size == 0:
                    print(f"Server {server_num}: ناحیه ایکن خالی")
                    continue

                print(f"Server {server_num}: بررسی ایکن در ناحیه ({icon_x_start},{icon_y_start}) تا ({icon_x_end},{icon_y_end})")

                # Analyze colors in the icon region
                best_color = None
                best_score = 0
                color_scores = {}

                for color_name, (lower, upper) in color_ranges.items():
                    lower = np.array(lower)
                    upper = np.array(upper)

                    # Create mask for this color in the icon region
                    color_mask = np.all((icon_region >= lower) & (icon_region <= upper), axis=2)
                    color_pixels = np.sum(color_mask)
                    color_scores[color_name] = color_pixels

                    if color_pixels > best_score:
                        best_score = color_pixels
                        best_color = color_name

                print(f"Server {server_num}: رنگ‌ها - {color_scores}")

                if best_color and best_score > 5:  # Lower threshold for small icons
                    # Calculate click position (center of button)
                    click_x = x + btn_width // 2
                    click_y = y + btn_height // 2

                    # Determine if this server is usable
                    is_usable = best_color in ['green', 'yellow', 'orange']
                    priority = 1 if best_color == 'green' else (2 if best_color == 'yellow' else (3 if best_color == 'orange' else 99))

                    server_candidates.append({
                        'server': server_num,
                        'color': best_color,
                        'score': best_score,
                        'position': (click_x, click_y),
                        'priority': priority,
                        'usable': is_usable,
                        'icon_area': (icon_x_start, icon_y_start, icon_x_end, icon_y_end)
                    })

                    status = "✅ قابل استفاده" if is_usable else "❌ غیرقابل استفاده"
                    print(f"Server {server_num}: رنگ {best_color}, امتیاز {best_score}, {status}")
                else:
                    print(f"Server {server_num}: هیچ رنگ مشخصی تشخیص داده نشد")

            # Sort candidates by priority (lower number = higher priority)
            server_candidates.sort(key=lambda x: (x['priority'], -x['score']))

            print(f"🎯 یافت شد {len(server_candidates)} سرور کاندید")

            # Show all detected servers
            for i, candidate in enumerate(server_candidates):
                status = "✅" if candidate['usable'] else "❌"
                print(f"  {i+1}. Server {candidate['server']}: {candidate['color']} (امتیاز: {candidate['score']}) {status}")

            # Filter for usable servers only (green, yellow, orange)
            usable_servers = [s for s in server_candidates if s['usable']]

            if not usable_servers:
                print("❌ هیچ سرور قابل استفاده‌ای یافت نشد!")
                print("💡 سرورهای قابل استفاده: سبز، زرد، نارنجی")
                return False

            # Select the best server (highest priority, then highest score)
            best_server = usable_servers[0]
            print(f"🎯 انتخاب بهترین سرور: Server {best_server['server']} با رنگ {best_server['color']} (امتیاز: {best_server['score']})")

            # Click on the best server
            click_x, click_y = best_server['position']
            print(f"🖱️ کلیک روی موقعیت: ({click_x}, {click_y})")

            # Perform the click
            self.click_at_position(port_number, click_x, click_y)

            # Wait a moment for the click to register
            time.sleep(1.5)

            print("✅ کلیک روی سرور انجام شد")
            return True

        except Exception as e:
            print(f"خطا در تشخیص سرور: {e}")
            return False

    def click_at_position(self, port_number, x, y):
        """Click at specific position using ADB"""
        try:
            import subprocess
            import os

            # Get ADB path
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists
            if not os.path.exists(adb_path):
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    adb_path = "adb"
                except (subprocess.SubprocessError, FileNotFoundError):
                    raise Exception("adb.exe یافت نشد")

            # Perform click
            click_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell input tap {x} {y}'
            click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True)
            print(f"🖱️ کلیک در موقعیت ({x}, {y}) انجام شد: {click_result.stdout}")

            # Wait a moment for the click to register
            time.sleep(1)
            return True

        except Exception as e:
            print(f"خطا در کلیک: {e}")
            return False

    def get_color_priority(self, color_name):
        """Get priority for server colors (lower number = higher priority)"""
        priority_map = {
            # Green colors (highest priority - best servers)
            'green': 1,

            # Yellow/Orange colors (medium priority - good servers)
            'yellow': 2,
            'orange': 3,

            # Red colors (lowest priority - avoid if possible)
            'red': 99,
        }
        return priority_map.get(color_name, 50)  # Default priority

    def detect_grid_pattern(self, img_array):
        """Detect grid-like pattern typical of server selection screens"""
        try:
            import numpy as np
            height, width = img_array.shape[:2]

            # Look for horizontal lines (server rows)
            horizontal_score = 0

            # Check for consistent horizontal bands
            for y in range(height // 4, 3 * height // 4, height // 10):
                # Sample a horizontal line
                line = img_array[y, :]

                # Look for color variations that suggest buttons
                color_changes = 0
                prev_color = line[0]
                for x in range(1, width):
                    current_color = line[x]
                    if np.linalg.norm(current_color - prev_color) > 50:  # Significant color change
                        color_changes += 1
                    prev_color = current_color

                # More color changes suggest button-like regions
                if color_changes > 4:  # At least 4 color transitions
                    horizontal_score += 1

            return horizontal_score

        except Exception as e:
            print(f"Error in detect_grid_pattern: {e}")
            return 0

    def analyze_server_layout(self, img_array):
        """Analyze layout characteristics typical of server selection"""
        try:
            import numpy as np
            height, width = img_array.shape[:2]

            layout_score = 0

            # Check for button-like rectangular regions
            # Look for areas with consistent colors (buttons)
            button_regions = 0

            # Divide screen into grid and check each cell
            rows, cols = 6, 3
            cell_height = height // rows
            cell_width = width // cols

            for row in range(1, rows - 1):  # Skip top and bottom rows
                for col in range(cols):
                    y_start = row * cell_height
                    y_end = (row + 1) * cell_height
                    x_start = col * cell_width
                    x_end = (col + 1) * cell_width

                    # Extract cell
                    cell = img_array[y_start:y_end, x_start:x_end]

                    if cell.size > 0:
                        # Check if cell has consistent color (like a button)
                        mean_color = np.mean(cell, axis=(0, 1))
                        color_variance = np.var(cell, axis=(0, 1))

                        # Low variance suggests solid color (button)
                        if np.mean(color_variance) < 1000:  # Relatively uniform color
                            button_regions += 1

            # More button-like regions = higher score
            if button_regions > 3:
                layout_score += 2
            elif button_regions > 1:
                layout_score += 1

            return layout_score

        except Exception as e:
            print(f"Error in analyze_server_layout: {e}")
            return 0

    def detect_loading_screen(self, screenshot_path):
        """Detect if the screenshot shows a loading screen - Enhanced for better detection"""
        try:
            from PIL import Image
            import numpy as np

            # Load the screenshot
            image = Image.open(screenshot_path)

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Convert to numpy array for analysis
            img_array = np.array(image)
            height, width = img_array.shape[:2]

            print(f"تحلیل صفحه loading با ابعاد: {width}x{height}")

            # Enhanced detection for the specific loading screen shown
            # The loading screen has:
            # 1. Very dark/black background (RGB values very low)
            # 2. White "LOADING..." text
            # 3. Colorful character (red hat, brown clothes, etc.)

            # Check for very dark background (stricter threshold)
            very_dark_threshold = 30  # Even stricter for black background
            very_dark_pixels = np.sum(np.all(img_array < very_dark_threshold, axis=2))
            total_pixels = width * height
            very_dark_ratio = very_dark_pixels / total_pixels

            # Also check for general dark background
            dark_threshold = 60
            dark_pixels = np.sum(np.all(img_array < dark_threshold, axis=2))
            dark_ratio = dark_pixels / total_pixels

            print(f"نسبت پیکسل‌های خیلی تاریک (< {very_dark_threshold}): {very_dark_ratio:.2f}")
            print(f"نسبت پیکسل‌های تاریک (< {dark_threshold}): {dark_ratio:.2f}")

            # Look for white text (LOADING...)
            white_threshold = 200
            white_pixels = np.sum(np.all(img_array > white_threshold, axis=2))
            white_ratio = white_pixels / total_pixels

            print(f"نسبت پیکسل‌های سفید: {white_ratio:.2f}")

            # Enhanced character detection - multiple color ranges
            character_pixels_total = 0

            # Red colors (hat)
            red_lower = np.array([120, 30, 30])
            red_upper = np.array([255, 120, 120])
            red_mask = np.all((img_array >= red_lower) & (img_array <= red_upper), axis=2)
            red_pixels = np.sum(red_mask)

            # Brown colors (clothes, skin)
            brown_lower = np.array([80, 40, 20])
            brown_upper = np.array([180, 120, 80])
            brown_mask = np.all((img_array >= brown_lower) & (img_array <= brown_upper), axis=2)
            brown_pixels = np.sum(brown_mask)

            # Yellow/gold colors (accessories)
            yellow_lower = np.array([150, 150, 50])
            yellow_upper = np.array([255, 255, 150])
            yellow_mask = np.all((img_array >= yellow_lower) & (img_array <= yellow_upper), axis=2)
            yellow_pixels = np.sum(yellow_mask)

            # Blue colors (possible clothing details)
            blue_lower = np.array([30, 50, 100])
            blue_upper = np.array([100, 150, 255])
            blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)
            blue_pixels = np.sum(blue_mask)

            character_pixels_total = red_pixels + brown_pixels + yellow_pixels + blue_pixels
            character_ratio = character_pixels_total / total_pixels

            print(f"پیکسل‌های قرمز: {red_pixels}, نسبت: {red_pixels/total_pixels:.3f}")
            print(f"پیکسل‌های قهوه‌ای: {brown_pixels}, نسبت: {brown_pixels/total_pixels:.3f}")
            print(f"پیکسل‌های زرد: {yellow_pixels}, نسبت: {yellow_pixels/total_pixels:.3f}")
            print(f"پیکسل‌های آبی: {blue_pixels}, نسبت: {blue_pixels/total_pixels:.3f}")
            print(f"مجموع پیکسل‌های شخصیت: {character_ratio:.3f}")

            # Enhanced loading screen detection criteria
            loading_detected = False
            detection_reasons = []

            # Primary detection: Very dark background + white text + character
            if very_dark_ratio > 0.7 and white_ratio > 0.005 and character_ratio > 0.02:
                loading_detected = True
                detection_reasons.append("معیار اصلی: پس‌زمینه خیلی تاریک + متن سفید + شخصیت")

            # Secondary detection: General dark background with good character detection
            elif dark_ratio > 0.8 and character_ratio > 0.03:
                loading_detected = True
                detection_reasons.append("معیار ثانویه: پس‌زمینه تاریک + شخصیت قوی")

            # Tertiary detection: Focus on character colors (for cases with different lighting)
            elif (red_pixels > 500 or brown_pixels > 1000) and dark_ratio > 0.6:
                loading_detected = True
                detection_reasons.append("معیار سوم: رنگ‌های شخصیت قوی + پس‌زمینه تاریک")

            if loading_detected:
                print("🎯 صفحه Loading تشخیص داده شد!")
                for reason in detection_reasons:
                    print(f"   - {reason}")
                return True
            else:
                print("❌ صفحه Loading تشخیص داده نشد")
                print("معیارهای تشخیص:")
                print(f"   - پس‌زمینه خیلی تاریک: {very_dark_ratio:.2f} (نیاز: > 0.7)")
                print(f"   - متن سفید: {white_ratio:.3f} (نیاز: > 0.005)")
                print(f"   - شخصیت: {character_ratio:.3f} (نیاز: > 0.02)")
                return False

        except Exception as e:
            print(f"خطا در تشخیص صفحه Loading: {e}")
            return False

    def detect_and_click_privacy_consent(self, port_id, port_number, screenshot_path):
        """Detect Privacy Information screen and click Save Settings button - Enhanced for better accuracy"""
        try:
            from PIL import Image
            import numpy as np

            # Load the screenshot
            image = Image.open(screenshot_path)

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Convert to numpy array for analysis
            img_array = np.array(image)
            height, width = img_array.shape[:2]

            print(f"تحلیل صفحه Privacy Information با ابعاد: {width}x{height}")

            # Enhanced detection for Privacy Information screen:
            # 1. Predominantly white/light background (80%+)
            # 2. Specific blue color for "Accept all" button
            # 3. Significant amount of dark text (Privacy Information content)
            # 4. Specific layout patterns

            # Calculate total pixels first
            total_pixels = width * height

            # Check for very light background (stricter threshold)
            very_light_threshold = 240  # Very white background
            very_light_pixels = np.sum(np.all(img_array > very_light_threshold, axis=2))
            very_light_ratio = very_light_pixels / total_pixels

            # General light background
            light_threshold = 200
            light_pixels = np.sum(np.all(img_array > light_threshold, axis=2))
            light_ratio = light_pixels / total_pixels

            print(f"نسبت پیکسل‌های خیلی روشن (> {very_light_threshold}): {very_light_ratio:.2f}")
            print(f"نسبت پیکسل‌های روشن (> {light_threshold}): {light_ratio:.2f}")

            # Enhanced blue button detection with more specific color range
            # The "Accept all" button has a specific blue color
            blue_lower = np.array([30, 100, 200])   # More specific blue range
            blue_upper = np.array([70, 130, 255])
            blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)
            blue_pixels = np.sum(blue_mask)
            blue_ratio = blue_pixels / total_pixels

            print(f"پیکسل‌های آبی (دکمه Accept): {blue_pixels}, نسبت: {blue_ratio:.3f}")

            # Look for dark text areas with better threshold
            dark_text_threshold = 80  # Stricter for black text
            dark_text_pixels = np.sum(np.all(img_array < dark_text_threshold, axis=2))
            dark_text_ratio = dark_text_pixels / total_pixels

            print(f"نسبت متن تاریک: {dark_text_ratio:.2f}")

            # Look for gray text (secondary text in privacy screen)
            gray_lower = np.array([80, 80, 80])
            gray_upper = np.array([150, 150, 150])
            gray_mask = np.all((img_array >= gray_lower) & (img_array <= gray_upper), axis=2)
            gray_pixels = np.sum(gray_mask)
            gray_ratio = gray_pixels / total_pixels

            print(f"نسبت متن خاکستری: {gray_ratio:.2f}")

            # Check for toggle switches (blue/gray toggles in privacy settings)
            toggle_blue_lower = np.array([50, 120, 200])
            toggle_blue_upper = np.array([90, 160, 255])
            toggle_mask = np.all((img_array >= toggle_blue_lower) & (img_array <= toggle_blue_upper), axis=2)
            toggle_pixels = np.sum(toggle_mask)
            toggle_ratio = toggle_pixels / total_pixels

            print(f"نسبت toggle آبی: {toggle_ratio:.3f}")

            # STRICT Privacy screen detection - ALL criteria must match for specific Privacy Information screen
            privacy_detected = False
            detection_reasons = []

            # Check for Accept all button with very specific blue color
            accept_blue_lower = np.array([40, 90, 200])   # More specific Accept all button blue
            accept_blue_upper = np.array([80, 130, 255])
            accept_blue_mask = np.all((img_array >= accept_blue_lower) & (img_array <= accept_blue_upper), axis=2)
            accept_blue_pixels = np.sum(accept_blue_mask)
            accept_blue_ratio = accept_blue_pixels / total_pixels

            print(f"پیکسل‌های آبی Accept all: {accept_blue_pixels}, نسبت: {accept_blue_ratio:.3f}")

            # Look for "Save Settings" text area characteristics
            # Save Settings button is typically light gray/white with dark text
            save_settings_lower = np.array([220, 220, 220])  # Light gray/white
            save_settings_upper = np.array([255, 255, 255])  # Pure white
            save_settings_mask = np.all((img_array >= save_settings_lower) & (img_array <= save_settings_upper), axis=2)
            save_settings_pixels = np.sum(save_settings_mask)
            save_settings_ratio = save_settings_pixels / total_pixels

            print(f"پیکسل‌های Save Settings (سفید): {save_settings_pixels}, نسبت: {save_settings_ratio:.3f}")

            # Look for toggle switches (blue toggles are characteristic of privacy screens)
            toggle_blue_lower = np.array([50, 120, 200])
            toggle_blue_upper = np.array([90, 160, 255])
            toggle_mask = np.all((img_array >= toggle_blue_lower) & (img_array <= toggle_blue_upper), axis=2)
            toggle_pixels = np.sum(toggle_mask)
            toggle_ratio = toggle_pixels / total_pixels

            print(f"پیکسل‌های toggle آبی: {toggle_pixels}, نسبت: {toggle_ratio:.3f}")

            # Check for "Privacy Information" title area (dark text on white)
            # Look for specific text patterns in upper area of screen
            upper_area = img_array[:height//3, :]  # Top third of screen
            dark_title_pixels = np.sum(np.all(upper_area < 100, axis=2))
            title_area_pixels = upper_area.shape[0] * upper_area.shape[1]
            title_text_ratio = dark_title_pixels / title_area_pixels if title_area_pixels > 0 else 0

            print(f"نسبت متن تیتل در بالای صفحه: {title_text_ratio:.3f}")

            # SIMPLE and RELIABLE detection criteria - only 3 essential criteria
            criteria_met = 0
            total_criteria = 3

            # Criterion 1: Very white background (80%+ of screen must be very light)
            if very_light_ratio > 0.8:
                criteria_met += 1
                detection_reasons.append("✓ معیار 1: پس‌زمینه خیلی سفید (>80%)")
            else:
                detection_reasons.append(f"✗ معیار 1: پس‌زمینه خیلی سفید ({very_light_ratio:.2f} < 0.8)")

            # Criterion 2: Save Settings button area (white button area)
            if save_settings_ratio > 0.1:  # At least 10% white area for button
                criteria_met += 1
                detection_reasons.append("✓ معیار 2: ناحیه Save Settings")
            else:
                detection_reasons.append(f"✗ معیار 2: ناحیه Save Settings ({save_settings_ratio:.3f} < 0.1)")

            # Criterion 3: Title text in upper area (Privacy Information)
            if title_text_ratio > 0.05 and title_text_ratio < 0.3:  # Some but not too much text
                criteria_met += 1
                detection_reasons.append("✓ معیار 3: متن تیتل Privacy Information")
            else:
                detection_reasons.append(f"✗ معیار 3: متن تیتل Privacy Information ({title_text_ratio:.3f} not in 0.05-0.3)")

            # Privacy screen detected if ALL 3 criteria are met
            if criteria_met >= 3:  # All 3 criteria must be met
                privacy_detected = True
                print(f"🎯 صفحه Privacy Information تشخیص داده شد! ({criteria_met}/{total_criteria} معیار برآورده شد)")
            else:
                print(f"❌ صفحه Privacy Information تشخیص داده نشد ({criteria_met}/{total_criteria} معیار برآورده شد)")

            # Show all criteria results
            for reason in detection_reasons:
                print(f"   {reason}")

            if not privacy_detected:
                return False

            if privacy_detected:
                # Simplified Save Settings button detection and click
                # Try to find blue button first, then use multiple methods to locate Save Settings

                # Method 1: Use the general blue mask
                blue_positions = np.where(blue_mask)
                accept_blue_positions = np.where(accept_blue_mask)

                # Choose the best blue button detection
                if len(accept_blue_positions[0]) > 0:
                    # Use Accept all specific blue detection
                    blue_center_y = int(np.mean(accept_blue_positions[0]))
                    blue_center_x = int(np.mean(accept_blue_positions[1]))
                    print(f"دکمه Accept all در موقعیت: ({blue_center_x}, {blue_center_y})")
                elif len(blue_positions[0]) > 0:
                    # Use general blue detection
                    blue_center_y = int(np.mean(blue_positions[0]))
                    blue_center_x = int(np.mean(blue_positions[1]))
                    print(f"دکمه آبی عمومی در موقعیت: ({blue_center_x}, {blue_center_y})")
                else:
                    # No blue button found, use screen center as fallback
                    blue_center_x = width // 2
                    blue_center_y = height * 2 // 3  # Assume button is in lower 2/3 of screen
                    print(f"دکمه آبی یافت نشد، استفاده از مرکز صفحه: ({blue_center_x}, {blue_center_y})")

                # Multiple methods to calculate Save Settings button position
                print(f"ابعاد صفحه: {width}x{height}")

                # Method 1: Standard offset (current method)
                save_button_x_method1 = blue_center_x
                save_button_y_method1 = blue_center_y + 70

                # Method 2: Percentage-based positioning
                # Save Settings is typically at 90% of screen height
                save_button_x_method2 = width // 2  # Center of screen
                save_button_y_method2 = int(height * 0.9)  # 90% down the screen

                # Method 3: Based on blue button position with different offsets
                save_button_x_method3 = blue_center_x
                save_button_y_method3 = blue_center_y + 100  # Larger offset

                # Method 4: Look for white/light areas below blue button (Save Settings area)
                search_start_y = blue_center_y + 30
                search_end_y = min(height - 30, blue_center_y + 150)

                # Find the lightest area in the search region (likely Save Settings button)
                max_light_y = search_start_y
                max_light_ratio = 0

                for y in range(search_start_y, search_end_y, 10):  # Check every 10 pixels
                    if y + 20 < height:  # Ensure we don't go out of bounds
                        # Check a small area around this Y coordinate
                        area = img_array[y:y+20, max(0, blue_center_x-50):min(width, blue_center_x+50)]
                        if area.size > 0:
                            light_pixels_in_area = np.sum(np.all(area > 200, axis=2))
                            total_pixels_in_area = area.shape[0] * area.shape[1]
                            if total_pixels_in_area > 0:
                                light_ratio_in_area = light_pixels_in_area / total_pixels_in_area
                                if light_ratio_in_area > max_light_ratio:
                                    max_light_ratio = light_ratio_in_area
                                    max_light_y = y + 10  # Center of the area

                save_button_x_method4 = blue_center_x
                save_button_y_method4 = max_light_y

                print(f"روش 1 (offset ثابت): ({save_button_x_method1}, {save_button_y_method1})")
                print(f"روش 2 (درصدی): ({save_button_x_method2}, {save_button_y_method2})")
                print(f"روش 3 (offset بزرگ): ({save_button_x_method3}, {save_button_y_method3})")
                print(f"روش 4 (تشخیص ناحیه روشن): ({save_button_x_method4}, {save_button_y_method4}) - نسبت روشنی: {max_light_ratio:.2f}")

                # Choose the best method based on conditions
                if max_light_ratio > 0.5:  # If we found a good light area
                    save_button_x = save_button_x_method4
                    save_button_y = save_button_y_method4
                    chosen_method = "روش 4 (تشخیص ناحیه روشن)"
                elif blue_center_y < height * 0.7:  # If blue button is in upper part
                    save_button_x = save_button_x_method1
                    save_button_y = save_button_y_method1
                    chosen_method = "روش 1 (offset ثابت)"
                else:  # If blue button is lower, use percentage method
                    save_button_x = save_button_x_method2
                    save_button_y = save_button_y_method2
                    chosen_method = "روش 2 (درصدی)"

                # Ensure coordinates are within screen bounds
                save_button_x = max(50, min(width - 50, save_button_x))
                save_button_y = max(50, min(height - 50, save_button_y))

                print(f"روش انتخاب شده: {chosen_method}")
                print(f"موقعیت نهایی دکمه Save Settings: ({save_button_x}, {save_button_y})")

                # Perform click on Save Settings button
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Try multiple click positions to increase success rate
                click_positions = [
                    (save_button_x, save_button_y),  # Primary calculated position
                    (save_button_x_method1, save_button_y_method1),  # Method 1
                    (save_button_x_method2, save_button_y_method2),  # Method 2
                    (save_button_x_method3, save_button_y_method3),  # Method 3
                    (width // 2, int(height * 0.88)),  # Center bottom
                    (width // 2, int(height * 0.92)),  # Even lower
                ]

                print("تلاش برای کلیک در موقعیت‌های مختلف...")

                for i, (click_x, click_y) in enumerate(click_positions):
                    # Ensure coordinates are within bounds
                    click_x = max(50, min(width - 50, click_x))
                    click_y = max(50, min(height - 50, click_y))

                    print(f"کلیک {i+1}: موقعیت ({click_x}, {click_y})")

                    # Perform click
                    click_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell input tap {click_x} {click_y}'
                    click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True)
                    print(f"نتیجه کلیک {i+1}: {click_result.stdout.strip() if click_result.stdout.strip() else 'موفق'}")

                    # Wait between clicks
                    time.sleep(0.5)

                    # If this is the primary position, wait a bit longer
                    if i == 0:
                        time.sleep(1)

                print("همه کلیک‌ها انجام شد. یکی از آن‌ها باید روی Save Settings بوده باشد.")
                return True

            return False

        except Exception as e:
            print(f"خطا در تشخیص Privacy Information: {e}")
            return False

    def detect_black_screen(self, screenshot_path):
        """Detect if the screenshot shows a black/dark screen"""
        try:
            from PIL import Image
            import numpy as np

            # Load the screenshot
            image = Image.open(screenshot_path)

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Convert to numpy array for analysis
            img_array = np.array(image)
            height, width = img_array.shape[:2]
            total_pixels = width * height

            print(f"تحلیل صفحه مشکی با ابعاد: {width}x{height}")

            # Check for very dark pixels (black/very dark gray)
            very_dark_threshold = 40  # Very dark colors
            very_dark_pixels = np.sum(np.all(img_array < very_dark_threshold, axis=2))
            very_dark_ratio = very_dark_pixels / total_pixels

            # Check for dark pixels (dark gray)
            dark_threshold = 80  # Dark gray colors
            dark_pixels = np.sum(np.all(img_array < dark_threshold, axis=2))
            dark_ratio = dark_pixels / total_pixels

            # Check for medium dark pixels (medium gray)
            medium_dark_threshold = 120  # Medium gray colors
            medium_dark_pixels = np.sum(np.all(img_array < medium_dark_threshold, axis=2))
            medium_dark_ratio = medium_dark_pixels / total_pixels

            print(f"نسبت پیکسل‌های خیلی تاریک (< {very_dark_threshold}): {very_dark_ratio:.2f}")
            print(f"نسبت پیکسل‌های تاریک (< {dark_threshold}): {dark_ratio:.2f}")
            print(f"نسبت پیکسل‌های نیمه تاریک (< {medium_dark_threshold}): {medium_dark_ratio:.2f}")

            # Black screen detection criteria
            black_screen_detected = False
            detection_reasons = []

            # Criterion 1: Very high ratio of very dark pixels (80%+ very dark)
            if very_dark_ratio > 0.8:
                black_screen_detected = True
                detection_reasons.append(f"✓ معیار 1: خیلی تاریک ({very_dark_ratio:.2f} > 0.8)")
            else:
                detection_reasons.append(f"✗ معیار 1: خیلی تاریک ({very_dark_ratio:.2f} < 0.8)")

            # Criterion 2: High ratio of dark pixels (90%+ dark)
            if dark_ratio > 0.9:
                black_screen_detected = True
                detection_reasons.append(f"✓ معیار 2: تاریک ({dark_ratio:.2f} > 0.9)")
            else:
                detection_reasons.append(f"✗ معیار 2: تاریک ({dark_ratio:.2f} < 0.9)")

            # Criterion 3: Very high ratio of medium dark pixels (95%+ medium dark)
            if medium_dark_ratio > 0.95:
                black_screen_detected = True
                detection_reasons.append(f"✓ معیار 3: نیمه تاریک ({medium_dark_ratio:.2f} > 0.95)")
            else:
                detection_reasons.append(f"✗ معیار 3: نیمه تاریک ({medium_dark_ratio:.2f} < 0.95)")

            # Show detection results
            if black_screen_detected:
                print("🎯 صفحه مشکی تشخیص داده شد!")
            else:
                print("❌ صفحه مشکی تشخیص داده نشد")

            for reason in detection_reasons:
                print(f"   {reason}")

            return black_screen_detected

        except Exception as e:
            print(f"Error in detect_black_screen: {e}")
            return False

    def click_at_position(self, port_number, x, y):
        """Click at specific position using ADB"""
        try:
            import subprocess
            import os
            import time

            # Get ADB path
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists
            if not os.path.exists(adb_path):
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    adb_path = "adb"
                except (subprocess.SubprocessError, FileNotFoundError):
                    raise Exception("adb.exe یافت نشد")

            # Perform click
            click_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell input tap {x} {y}'
            click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True)
            print(f"🖱️ کلیک در موقعیت ({x}, {y}) انجام شد")

            # Wait a moment for the click to register
            time.sleep(1)
            return True

        except Exception as e:
            print(f"خطا در کلیک: {e}")
            return False

    def detect_wallet_address(self, port_number, screenshot_path):
        """Detect wallet address from screenshot"""
        try:
            from PIL import Image, ImageEnhance
            import pytesseract
            import numpy as np
            import re

            # Open the screenshot
            image = Image.open(screenshot_path)
            width, height = image.size

            print(f"🔍 تشخیص آدرس والت در تصویر {width}x{height}")

            # Look for "Account" text area - usually in the middle-bottom area
            # Based on the image, Account section is in the blue area
            account_area_y_start = int(height * 0.3)  # Start from 30% of height
            account_area_y_end = int(height * 0.7)    # End at 70% of height

            # Crop the account area
            account_area = image.crop((0, account_area_y_start, width, account_area_y_end))

            # Enhance the image for better OCR
            enhancer = ImageEnhance.Contrast(account_area)
            enhanced = enhancer.enhance(2.0)

            # Convert to grayscale for better text recognition
            gray = enhanced.convert('L')

            # Apply threshold to make text clearer
            threshold = 128
            gray_array = np.array(gray)
            binary = np.where(gray_array > threshold, 255, 0).astype(np.uint8)
            binary_image = Image.fromarray(binary)

            # Try multiple OCR configurations
            ocr_configs = [
                r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFabcdef',
                r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789ABCDEFabcdef',
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFabcdef',
                r'--oem 1 --psm 6'
            ]

            detected_addresses = []

            for i, config in enumerate(ocr_configs):
                try:
                    # Try on enhanced image
                    ocr_text = pytesseract.image_to_string(enhanced, config=config).strip()
                    print(f"OCR تلاش {i+1} (enhanced): '{ocr_text}'")

                    # Try on binary image
                    ocr_text_binary = pytesseract.image_to_string(binary_image, config=config).strip()
                    print(f"OCR تلاش {i+1} (binary): '{ocr_text_binary}'")

                    # Combine both results
                    combined_text = ocr_text + "\n" + ocr_text_binary

                    # Look for wallet address patterns (0x followed by 40 hex characters)
                    wallet_pattern = r'0x[a-fA-F0-9]{40}'
                    matches = re.findall(wallet_pattern, combined_text)

                    if matches:
                        for match in matches:
                            if match not in detected_addresses:
                                detected_addresses.append(match)
                                print(f"✅ آدرس والت تشخیص داده شد: {match}")

                    # Also look for partial addresses or addresses without 0x
                    hex_pattern = r'[a-fA-F0-9]{40}'
                    hex_matches = re.findall(hex_pattern, combined_text)

                    for hex_match in hex_matches:
                        full_address = "0x" + hex_match
                        if full_address not in detected_addresses:
                            detected_addresses.append(full_address)
                            print(f"✅ آدرس والت (بدون 0x) تشخیص داده شد: {full_address}")

                except Exception as e:
                    print(f"خطا در OCR تلاش {i+1}: {e}")
                    continue

            if detected_addresses:
                return detected_addresses[0]  # Return the first detected address
            else:
                print("❌ هیچ آدرس والت تشخیص داده نشد")
                return None

        except Exception as e:
            print(f"خطا در تشخیص آدرس والت: {e}")
            return None

    def show_wallet_address_detection(self):
        """Show wallet address detection for selected port"""
        try:
            selected_items = self.scan_tree.selection()
            if not selected_items:
                messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
                return

            # Get selected port display (could be number or title)
            item = self.scan_tree.item(selected_items[0])
            port_display = item['values'][1]

            # Find the actual port number
            port_number = self.get_port_number_from_display(port_display)
            if not port_number:
                messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
                return

            # Take fresh screenshot
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            screenshot_path = temp_file.name
            temp_file.close()

            if not self.take_screenshot_to_path(port_number, screenshot_path):
                messagebox.showerror("خطا", "خطا در گرفتن اسکرین‌شات")
                return

            # Detect wallet address
            wallet_address = self.detect_wallet_address(port_number, screenshot_path)

            # Clean up temp file
            try:
                import os
                os.unlink(screenshot_path)
            except:
                pass

            if wallet_address:
                # Show result with copy option
                result_dialog = tk.Toplevel(self)
                result_dialog.title("آدرس والت تشخیص داده شده")
                result_dialog.geometry("600x200")
                self.center_window_on_parent(result_dialog, 600, 200)
                result_dialog.resizable(False, False)

                # Center the dialog
                result_dialog.transient(self)
                result_dialog.grab_set()

                # Main frame
                main_frame = ttk.Frame(result_dialog, padding=20)
                main_frame.pack(fill='both', expand=True)

                # Title
                title_label = ttk.Label(main_frame, text=f"آدرس والت پورت {port_number}:",
                                      font=('Arial', 12, 'bold'))
                title_label.pack(pady=(0, 10))

                # Address display
                address_frame = ttk.Frame(main_frame)
                address_frame.pack(fill='x', pady=10)

                address_entry = ttk.Entry(address_frame, font=('Courier', 10), width=50)
                address_entry.insert(0, wallet_address)
                address_entry.config(state='readonly')
                address_entry.pack(side='left', padx=(0, 10))

                # Copy button
                def copy_address():
                    result_dialog.clipboard_clear()
                    result_dialog.clipboard_append(wallet_address)
                    messagebox.showinfo("کپی شد", "آدرس والت کپی شد!")

                copy_btn = ttk.Button(address_frame, text="کپی", command=copy_address)
                copy_btn.pack(side='left')

                # Close button
                close_btn = ttk.Button(main_frame, text="بستن", command=result_dialog.destroy)
                close_btn.pack(pady=10)

            else:
                messagebox.showinfo("نتیجه", f"آدرس والت در پورت {port_number} تشخیص داده نشد.\n\nممکن است:\n- صفحه Account باز نباشد\n- کیفیت تصویر مناسب نباشد\n- آدرس در ناحیه مورد انتظار نباشد")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در تشخیص آدرس والت: {e}")

    def scan_all_wallet_addresses(self):
        """Scan wallet addresses for all ports and save to database"""
        try:
            # Get default group
            default_group = self.get_default_group()

            # Get all ports from default group
            self.cursor.execute("""
                SELECT id, port_number, title
                FROM ports
                WHERE group_number = ?
                ORDER BY port_number
            """, (default_group,))

            ports = self.cursor.fetchall()

            if not ports:
                messagebox.showinfo("اطلاعات", "هیچ پورتی یافت نشد.")
                return

            # Create progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("اسکن آدرس والت‌ها")
            progress_window.geometry("500x200")
            self.center_window_on_parent(progress_window, 500, 200)
            progress_window.transient(self)
            progress_window.grab_set()

            # Progress frame
            progress_frame = ttk.Frame(progress_window)
            progress_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # Status label
            status_label = ttk.Label(progress_frame, text="آماده‌سازی...", font=('Tahoma', 12))
            status_label.pack(pady=10)

            # Progress bar
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=len(ports))
            progress_bar.pack(fill='x', pady=10)

            # Results label
            results_label = ttk.Label(progress_frame, text="", font=('Tahoma', 10))
            results_label.pack(pady=5)

            # Cancel button
            cancel_flag = {'cancelled': False}

            def cancel_scan():
                cancel_flag['cancelled'] = True
                progress_window.destroy()

            cancel_btn = ttk.Button(progress_frame, text="لغو", command=cancel_scan)
            cancel_btn.pack(pady=10)

            # Update progress window
            progress_window.update()

            # Scan each port
            successful_scans = 0
            failed_scans = 0

            for i, (port_id, port_number, title) in enumerate(ports):
                if cancel_flag['cancelled']:
                    break

                # Update status
                display_name = title if title else f"پورت {port_number}"
                status_label.config(text=f"اسکن {display_name}...")
                progress_var.set(i)
                progress_window.update()

                try:
                    # Take screenshot
                    screenshot_path = f"screenshots/wallet_scan_{port_number}.png"
                    if self.take_screenshot_to_path(port_number, screenshot_path):
                        # Detect wallet address
                        wallet_address = self.detect_wallet_address(port_number, screenshot_path)

                        if wallet_address:
                            # Save to wallet database
                            self.save_wallet_address_to_db(wallet_address, port_number, port_id)
                            successful_scans += 1
                            results_label.config(text=f"موفق: {successful_scans}, ناموفق: {failed_scans}")
                        else:
                            failed_scans += 1
                            results_label.config(text=f"موفق: {successful_scans}, ناموفق: {failed_scans}")
                    else:
                        failed_scans += 1
                        results_label.config(text=f"موفق: {successful_scans}, ناموفق: {failed_scans}")

                except Exception as e:
                    print(f"Error scanning port {port_number}: {e}")
                    failed_scans += 1
                    results_label.config(text=f"موفق: {successful_scans}, ناموفق: {failed_scans}")

                # Small delay between scans
                time.sleep(0.5)

            # Final update
            progress_var.set(len(ports))
            if not cancel_flag['cancelled']:
                status_label.config(text="اسکن کامل شد!")
                results_label.config(text=f"نتیجه نهایی - موفق: {successful_scans}, ناموفق: {failed_scans}")

                # Change cancel button to close
                cancel_btn.config(text="بستن", command=progress_window.destroy)

                # Update connection status
                self.update_connection_status()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در اسکن آدرس والت‌ها: {e}")

    def center_window_on_parent(self, window, width, height):
        """Center a window on its parent"""
        try:
            # Get parent window position and size
            parent_x = self.winfo_x()
            parent_y = self.winfo_y()
            parent_width = self.winfo_width()
            parent_height = self.winfo_height()

            # Calculate position to center on parent
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2

            # Set geometry
            window.geometry(f"{width}x{height}+{x}+{y}")
        except Exception as e:
            print(f"Error centering window on parent: {e}")

    def save_wallet_address_to_db(self, wallet_address, port_number, port_id):
        """Save wallet address to database"""
        try:
            # Check if address already exists
            conn = sqlite3.connect(WALLET_DB_PATH)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM wallet_addresses
                WHERE address = ? AND port_number = ?
            """, (wallet_address, port_number))

            existing = cursor.fetchone()

            if existing:
                # Update existing record
                cursor.execute("""
                    UPDATE wallet_addresses
                    SET last_seen = ?, port_id = ?
                    WHERE id = ?
                """, (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), port_id, existing[0]))
            else:
                # Insert new record
                cursor.execute("""
                    INSERT INTO wallet_addresses (address, port_number, port_id, first_seen, last_seen)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    wallet_address,
                    port_number,
                    port_id,
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Error saving wallet address to database: {e}")

    def take_screenshot_to_path(self, port_number, output_path):
        """Take screenshot and save to specific path"""
        try:
            # Try to use adb directly
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists in the specified path
            if not os.path.exists(adb_path):
                # Try to find adb in PATH
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    # If we get here, adb is in PATH
                    adb_path = "adb"
                except (subprocess.SubprocessError, FileNotFoundError):
                    return False

            # Check if device is connected
            devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)

            if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                # Try to connect to the device
                connect_cmd = f'{adb_path} connect 127.0.0.1:{port_number}'
                subprocess.run(connect_cmd, shell=True, capture_output=True, text=True)

                # Check again if connected
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    return False

            # Take screenshot using adb
            screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
            subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)

            # Pull screenshot from device
            pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png "{output_path}"'
            subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)

            # Check if screenshot was taken
            return os.path.exists(output_path)

        except Exception as e:
            print(f"Error in take_screenshot_to_path: {e}")
            return False

    def save_current_screenshot(self):
        """Save current screenshot manually"""
        try:
            selected_items = self.scan_tree.selection()
            if not selected_items:
                messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
                return

            # Get selected port display (could be number or title)
            item = self.scan_tree.item(selected_items[0])
            port_display = item['values'][1]

            # Find the actual port number
            port_number = self.get_port_number_from_display(port_display)
            if not port_number:
                messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
                return

            # Take screenshot and save to screenshots directory
            import os
            screenshot_path = os.path.join(SCREENSHOT_DIR, f"port_{port_number}.png")

            if self.take_screenshot_to_path(port_number, screenshot_path):
                messagebox.showinfo("موفقیت", f"اسکرین ذخیره شد:\n{screenshot_path}")
            else:
                messagebox.showerror("خطا", "خطا در گرفتن اسکرین‌شات")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره اسکرین: {e}")

    def detect_current_screen(self):
        """Detect current screen and suggest action"""
        try:
            selected_items = self.scan_tree.selection()
            if not selected_items:
                messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
                return

            # Get selected port display (could be number or title)
            item = self.scan_tree.item(selected_items[0])
            port_display = item['values'][1]

            # Find the actual port number
            port_number = self.get_port_number_from_display(port_display)
            if not port_number:
                messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
                return

            # Take fresh screenshot
            import tempfile
            from PIL import Image
            import numpy as np

            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            screenshot_path = temp_file.name
            temp_file.close()

            if not self.take_screenshot_to_path(port_number, screenshot_path):
                messagebox.showerror("خطا", "خطا در گرفتن اسکرین‌شات")
                return

            # Analyze screen based on checkbox order
            results = []

            # 1. Open KKT - Check if KKT app is running using ADB
            try:
                import subprocess
                import os

                # Get ADB path
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")
                if not os.path.exists(adb_path):
                    try:
                        subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                    except (subprocess.SubprocessError, FileNotFoundError):
                        results.append("❌ خطا: adb یافت نشد")
                        raise Exception("adb not found")

                # Check running apps using ADB
                cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "ps | grep com.king"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                if result.returncode == 0 and "com.king" in result.stdout:
                    results.append("✅ برنامه KKT باز است (تأیید شده با ADB)")
                else:
                    # Try alternative method - check current activity
                    cmd2 = f'{adb_path} -s 127.0.0.1:{port_number} shell "dumpsys window windows | grep -E mCurrentFocus"'
                    result2 = subprocess.run(cmd2, shell=True, capture_output=True, text=True, timeout=5)

                    if result2.returncode == 0 and ("com.king" in result2.stdout or "kkt" in result2.stdout.lower()):
                        results.append("✅ برنامه KKT باز است (تأیید شده با ADB)")
                    else:
                        results.append("❌ برنامه KKT بسته است - باید باز شود")

            except subprocess.TimeoutExpired:
                results.append("❌ خطا: timeout در بررسی وضعیت KKT")
            except Exception as e:
                results.append(f"❌ خطا در بررسی وضعیت KKT: {str(e)[:50]}")

            # 2. Click Enter - Check for Enter button (fast check only)
            try:
                image = Image.open(screenshot_path)
                img_array = np.array(image)

                # Quick blue button detection (Enter button is usually blue)
                blue_lower = np.array([100, 180, 220])
                blue_upper = np.array([180, 255, 255])
                blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)
                blue_pixels = np.sum(blue_mask)

                if blue_pixels > 50:
                    results.append("✅ دکمه آبی (احتمالاً Enter) موجود است")
                else:
                    results.append("❌ دکمه Enter یافت نشد")
            except Exception as e:
                results.append(f"❌ خطا در تشخیص دکمه Enter: {str(e)[:30]}")

            # 3. Detect Server - Check for server selection screen
            try:
                image = Image.open(screenshot_path)
                img_array = np.array(image)

                # Look for blue pixels (server selection indicators)
                blue_lower = np.array([100, 150, 200])
                blue_upper = np.array([180, 255, 255])
                blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)
                blue_pixels = np.sum(blue_mask)

                # Look for orange/yellow pixels (server status indicators)
                orange_lower = np.array([200, 100, 0])
                orange_upper = np.array([255, 200, 100])
                orange_mask = np.all((img_array >= orange_lower) & (img_array <= orange_upper), axis=2)
                orange_pixels = np.sum(orange_mask)

                if blue_pixels > 1000 and orange_pixels > 100:
                    results.append("✅ صفحه انتخاب سرور تشخیص داده شد - باید سرور انتخاب شود")
                else:
                    results.append("❌ صفحه انتخاب سرور نیست")
            except Exception as e:
                results.append(f"❌ خطا در تشخیص صفحه سرور: {str(e)[:30]}")

            # 4. Ad Permission - Check for Privacy Information
            try:
                # Simple text-based detection
                results.append("❌ تشخیص Privacy Information - نیاز به بررسی دستی")
            except:
                results.append("❌ خطا در تشخیص Privacy Information")

            # 5. Close Loading - Check for loading screen
            try:
                # Check for loading indicators (spinning elements, progress bars)
                results.append("❌ صفحه Loading تشخیص داده نشد")
            except:
                results.append("❌ خطا در تشخیص Loading")

            # 6. Detect Black Screen
            try:
                image = Image.open(screenshot_path)
                img_array = np.array(image)

                # Calculate average brightness
                gray = np.mean(img_array, axis=2)
                avg_brightness = np.mean(gray)

                if avg_brightness < 30:  # Very dark screen
                    results.append("✅ صفحه مشکی تشخیص داده شد - باید KKT بسته شود")
                else:
                    results.append(f"❌ صفحه مشکی نیست (روشنایی: {avg_brightness:.1f})")
            except Exception as e:
                results.append(f"❌ خطا در تشخیص صفحه مشکی: {str(e)[:30]}")

            # 7. Play Ads - Check for speaker icon
            try:
                # Look for speaker icon in bottom area
                results.append("❌ ایکن بلندگو یافت نشد")
            except:
                results.append("❌ خطا در تشخیص ایکن بلندگو")

            # Clean up temp file
            try:
                import os
                os.unlink(screenshot_path)
            except:
                pass

            # Show results
            result_text = f"🎯 تحلیل صفحه فعلی پورت {port_number}:\n\n" + "\n".join(results)
            messagebox.showinfo("نتیجه تشخیص صفحه", result_text)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در تشخیص صفحه: {e}")

    def detect_and_click_enter_button(self, port_id, port_number, screenshot_path):
        """Detect and click Enter button in the login screen"""
        try:
            from PIL import Image
            import numpy as np
            import pytesseract

            # Load the screenshot
            image = Image.open(screenshot_path)

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Convert to numpy array for analysis
            img_array = np.array(image)
            height, width = img_array.shape[:2]

            print(f"تحلیل صفحه ورود با ابعاد: {width}x{height}")

            # Look for blue button area (Enter button is blue)
            # Based on your image, the Enter button is bright blue and located in the lower part
            # Adjusted color range for the bright blue Enter button
            blue_lower = np.array([100, 180, 220])  # Brighter blue color range
            blue_upper = np.array([180, 255, 255])
            blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)

            # Find blue regions
            blue_pixels = np.sum(blue_mask)
            print(f"تعداد پیکسل‌های آبی: {blue_pixels}")

            # Try multiple blue color ranges if first one doesn't work
            if blue_pixels < 50:  # Lower threshold
                print("تلاش با رنج رنگی دوم...")
                # Try a different blue range (more cyan-like)
                blue_lower2 = np.array([80, 160, 200])
                blue_upper2 = np.array([160, 240, 255])
                blue_mask = np.all((img_array >= blue_lower2) & (img_array <= blue_upper2), axis=2)
                blue_pixels = np.sum(blue_mask)
                print(f"تعداد پیکسل‌های آبی (رنج دوم): {blue_pixels}")

            if blue_pixels < 50:  # Still not enough, try third range
                print("تلاش با رنج رنگی سوم...")
                # Try an even broader blue range
                blue_lower3 = np.array([60, 140, 180])
                blue_upper3 = np.array([200, 255, 255])
                blue_mask = np.all((img_array >= blue_lower3) & (img_array <= blue_upper3), axis=2)
                blue_pixels = np.sum(blue_mask)
                print(f"تعداد پیکسل‌های آبی (رنج سوم): {blue_pixels}")

            if blue_pixels < 30:  # Final minimum threshold
                print("دکمه آبی (Enter) تشخیص داده نشد - پیکسل‌های آبی کافی نیست")
                return False

            # Find the center of blue regions (potential Enter button)
            blue_positions = np.where(blue_mask)
            if len(blue_positions[0]) == 0:
                print("موقعیت دکمه آبی یافت نشد")
                return False

            # Calculate center of blue region
            center_y = int(np.mean(blue_positions[0]))
            center_x = int(np.mean(blue_positions[1]))

            print(f"مرکز منطقه آبی: ({center_x}, {center_y})")

            # Focus on the lower part of the screen where Enter button should be
            # Allow more flexibility - check if it's in bottom 60% of screen
            lower_threshold = height * 0.4  # Bottom 60% of screen
            if center_y < lower_threshold:
                print(f"دکمه آبی در قسمت بالایی صفحه است (y={center_y}, threshold={lower_threshold}) - احتمالاً Enter نیست")
                # Don't return False immediately, continue with OCR check
                print("ادامه با بررسی OCR...")
            else:
                print(f"دکمه آبی در موقعیت مناسب قرار دارد (y={center_y})")

            # Extract region around the blue area for OCR
            margin = 50
            x_start = max(0, center_x - margin)
            y_start = max(0, center_y - margin)
            x_end = min(width, center_x + margin)
            y_end = min(height, center_y + margin)

            button_region = image.crop((x_start, y_start, x_end, y_end))

            # Use OCR to detect "Enter" text
            try:
                # Try multiple OCR configurations for better detection
                ocr_configs = [
                    r'--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
                    r'--oem 3 --psm 7',
                    r'--oem 3 --psm 6',
                    r'--oem 1 --psm 8'
                ]

                ocr_text = ""
                for i, config in enumerate(ocr_configs):
                    try:
                        ocr_text = pytesseract.image_to_string(button_region, config=config).strip().lower()
                        print(f"OCR تلاش {i+1}: '{ocr_text}'")
                        if "enter" in ocr_text:
                            break
                    except:
                        continue

                print(f"متن نهایی تشخیص داده شده: '{ocr_text}'")

                # Check if "enter" text is detected (more flexible matching)
                enter_found = ("enter" in ocr_text or
                              "ente" in ocr_text or
                              "nter" in ocr_text or
                              "entr" in ocr_text)

                if enter_found:
                    print("✓ متن 'Enter' تشخیص داده شد!")

                    # Perform click on the center of blue region
                    click_x = center_x
                    click_y = center_y

                    print(f"کلیک روی دکمه Enter در موقعیت: ({click_x}, {click_y})")

                    # Use adb to perform the click
                    adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                    # Check if adb exists
                    if not os.path.exists(adb_path):
                        try:
                            adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                            adb_path = "adb"
                        except (subprocess.SubprocessError, FileNotFoundError):
                            raise Exception("adb.exe یافت نشد")

                    # Perform the click
                    click_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell input tap {click_x} {click_y}'
                    click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True)
                    print(f"نتیجه کلیک: {click_result.stdout}")

                    if click_result.returncode == 0:
                        print("✓ کلیک روی دکمه Enter با موفقیت انجام شد")
                        return True
                    else:
                        print(f"✗ خطا در کلیک: {click_result.stderr}")
                        return False

                else:
                    print("✗ متن 'Enter' در منطقه آبی تشخیص داده نشد")
                    # Don't return False immediately, try fallback
                    print("تلاش fallback بر اساس موقعیت و رنگ...")

            except Exception as ocr_error:
                print(f"خطا در OCR: {ocr_error}")
                print("تلاش fallback بر اساس موقعیت و رنگ...")

            # Fallback: Click on blue region based on position and color
            # if it's in the lower part of screen and has enough blue pixels
            if center_y > height * 0.5 and blue_pixels > 30:  # In bottom 50% of screen with enough blue
                print(f"تلاش برای کلیک بدون تأیید OCR (موقعیت: y={center_y}, آبی: {blue_pixels})")

                click_x = center_x
                click_y = center_y

                # Use adb to perform the click
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Perform the click
                click_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell input tap {click_x} {click_y}'
                click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True)
                print(f"نتیجه کلیک fallback: {click_result.stdout}")

                if click_result.returncode == 0:
                    print("✓ کلیک fallback روی دکمه آبی با موفقیت انجام شد")
                    return True
                else:
                    print(f"✗ خطا در کلیک fallback: {click_result.stderr}")
                    return False
            else:
                print("دکمه آبی در موقعیت مناسب Enter قرار ندارد")
                return False

        except Exception as e:
            print(f"Error in detect_and_click_enter_button: {e}")
            return False

    def detect_privacy_for_selected_port(self):
        """Detect Privacy Information screen for selected port and click Save Settings"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفاً یک پورت انتخاب کنید.")
            return

        # Get the selected port information
        item = selected_items[0]
        values = self.scan_tree.item(item, 'values')
        if not values:
            messagebox.showwarning("هشدار", "اطلاعات پورت یافت نشد.")
            return

        port_number = values[1]  # Port number is in the second column

        # Get actual port number from database
        try:
            cursor = self.conn.cursor()

            # First try to find by exact match (if it's already a port number)
            if isinstance(port_number, str) and port_number.isdigit():
                cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (int(port_number),))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            # If it contains "MuMu", try to find by title
            elif isinstance(port_number, str) and "MuMu" in port_number:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    # Try to extract number and find by title pattern
                    try:
                        mumu_number = port_number.replace("MuMu", "").strip()
                        if mumu_number.isdigit():
                            cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {mumu_number}",))
                            result = cursor.fetchone()
                            if result:
                                port_id, actual_port_number = result
                                port_number = actual_port_number
                            else:
                                messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                                return
                        else:
                            messagebox.showerror("خطا", f"شماره پورت نامعتبر: {port_number}")
                            return
                    except:
                        messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {port_number}")
                        return

            # Try to find by title (in case it's a custom title)
            else:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            print(f"استفاده از پورت واقعی: {port_number} (ID: {port_id})")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("تشخیص Privacy Information")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress label
        progress_label = ttk.Label(progress_window, text=f"در حال تشخیص Privacy Information برای پورت {port_number}...")
        progress_label.pack(pady=20)

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        def perform_detection():
            try:
                # Take screenshot using improved method
                screenshot_path = f"screenshot_{port_number}.png"
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                        print(f"Using adb from PATH for screenshot")
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Check if device is connected
                print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"دستگاه 127.0.0.1:{port_number} متصل نیست")

                # Take screenshot using improved method (same as other screenshot functions)
                print(f"Taking screenshot for port {port_number}...")

                # Method 1: Try screencap to file first
                screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
                screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
                print(f"Screencap result: {screencap_result.stdout}")

                # Pull the file
                pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png {screenshot_path}'
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
                print(f"Pull result: {pull_result.stdout}")

                # Check if screenshot file exists
                if not os.path.exists(screenshot_path):
                    raise Exception(f"فایل screenshot ایجاد نشد: {screenshot_path}")

                print(f"Screenshot saved to {screenshot_path}")

                # Detect Privacy Information and click Save Settings
                result = self.detect_and_click_privacy_consent(port_id, port_number, screenshot_path)

                # Clean up screenshot
                try:
                    os.remove(screenshot_path)
                except:
                    pass

                # Update UI in main thread
                def update_ui():
                    progress_window.destroy()
                    if result:
                        messagebox.showinfo("موفقیت", f"صفحه Privacy Information در پورت {port_number} تشخیص داده شد و Save Settings کلیک شد.")
                    else:
                        messagebox.showinfo("اطلاع", f"صفحه Privacy Information در پورت {port_number} تشخیص داده نشد.")

                self.after(0, update_ui)

            except Exception as e:
                error_message = str(e)
                def show_error():
                    progress_window.destroy()
                    messagebox.showerror("خطا", f"خطا در تشخیص Privacy Information: {error_message}")

                self.after(0, show_error)

        # Run detection in a separate thread
        import threading
        detection_thread = threading.Thread(target=perform_detection)
        detection_thread.daemon = True
        detection_thread.start()

    def detect_black_screen_for_selected_port(self):
        """Detect black screen for selected port and close KKT if found"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        values = item['values']

        if not values:
            messagebox.showwarning("هشدار", "اطلاعات پورت یافت نشد.")
            return

        port_number = values[1]  # Port number is in the second column

        # Get actual port number from database
        try:
            cursor = self.conn.cursor()

            # First try to find by exact match (if it's already a port number)
            if isinstance(port_number, str) and port_number.isdigit():
                cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (int(port_number),))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            # If it contains "MuMu", try to find by title
            elif isinstance(port_number, str) and "MuMu" in port_number:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    # Try to extract number and find by title pattern
                    try:
                        mumu_number = port_number.replace("MuMu", "").strip()
                        if mumu_number.isdigit():
                            cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {mumu_number}",))
                            result = cursor.fetchone()
                            if result:
                                port_id, actual_port_number = result
                                port_number = actual_port_number
                            else:
                                messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                                return
                        else:
                            messagebox.showerror("خطا", f"شماره پورت نامعتبر: {port_number}")
                            return
                    except:
                        messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {port_number}")
                        return

            # Try to find by title (in case it's a custom title)
            else:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            print(f"استفاده از پورت واقعی: {port_number} (ID: {port_id})")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("تشخیص صفحه مشکی")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress label
        progress_label = ttk.Label(progress_window, text=f"در حال تشخیص صفحه مشکی برای پورت {port_number}...")
        progress_label.pack(pady=20)

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        def perform_detection():
            try:
                # Take screenshot using improved method
                screenshot_path = f"screenshot_{port_number}.png"
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                        print(f"Using adb from PATH for screenshot")
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Check if device is connected
                print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"دستگاه 127.0.0.1:{port_number} متصل نیست")

                # Take screenshot using improved method (same as other screenshot functions)
                print(f"Taking screenshot for port {port_number}...")

                # Method 1: Try screencap to file first
                screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
                screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
                print(f"Screencap result: {screencap_result.stdout}")

                # Pull the file
                pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png {screenshot_path}'
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
                print(f"Pull result: {pull_result.stdout}")

                # Check if screenshot file exists
                if not os.path.exists(screenshot_path):
                    raise Exception(f"فایل screenshot ایجاد نشد: {screenshot_path}")

                print(f"Screenshot saved to {screenshot_path}")

                # Detect black screen
                result = self.detect_black_screen(screenshot_path)

                def update_ui():
                    progress_window.destroy()
                    if result:
                        # Ask user if they want to close KKT
                        response = messagebox.askyesno("صفحه مشکی تشخیص داده شد",
                                                    f"صفحه مشکی در پورت {port_number} تشخیص داده شد.\n\n"
                                                    f"آیا می‌خواهید برنامه KKT را ببندید؟")
                        if response:
                            try:
                                self.close_kkt_app_for_port(port_id, port_number)
                                messagebox.showinfo("موفقیت", f"برنامه KKT در پورت {port_number} بسته شد.")
                            except Exception as e:
                                messagebox.showerror("خطا", f"خطا در بستن KKT: {e}")
                        else:
                            messagebox.showinfo("اطلاع", f"صفحه مشکی تشخیص داده شد ولی KKT بسته نشد.")
                    else:
                        messagebox.showinfo("اطلاع", f"صفحه مشکی در پورت {port_number} تشخیص داده نشد.")

                self.after(0, update_ui)

            except Exception as e:
                error_message = str(e)
                def show_error():
                    progress_window.destroy()
                    messagebox.showerror("خطا", f"خطا در تشخیص صفحه مشکی: {error_message}")

                self.after(0, show_error)

        # Run detection in a separate thread
        import threading
        detection_thread = threading.Thread(target=perform_detection)
        detection_thread.daemon = True
        detection_thread.start()

    def detect_enter_for_selected_port(self):
        """Detect Enter button for selected port and click it"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        values = item['values']

        if not values:
            messagebox.showwarning("هشدار", "اطلاعات پورت یافت نشد.")
            return

        port_number = values[1]  # Port number is in the second column

        # Get actual port number from database
        try:
            cursor = self.conn.cursor()

            # First try to find by exact match (if it's already a port number)
            if isinstance(port_number, str) and port_number.isdigit():
                cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (int(port_number),))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            # If it contains "MuMu", try to find by title
            elif isinstance(port_number, str) and "MuMu" in port_number:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    # Try to extract number and find by title pattern
                    try:
                        mumu_number = port_number.replace("MuMu", "").strip()
                        if mumu_number.isdigit():
                            cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {mumu_number}",))
                            result = cursor.fetchone()
                            if result:
                                port_id, actual_port_number = result
                                port_number = actual_port_number
                            else:
                                messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                                return
                        else:
                            messagebox.showerror("خطا", f"شماره پورت نامعتبر: {port_number}")
                            return
                    except:
                        messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {port_number}")
                        return

            # Try to find by title (in case it's a custom title)
            else:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            print(f"استفاده از پورت واقعی: {port_number} (ID: {port_id})")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("تشخیص دکمه Enter")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress label
        progress_label = ttk.Label(progress_window, text=f"در حال تشخیص دکمه Enter برای پورت {port_number}...")
        progress_label.pack(pady=20)

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        def perform_detection():
            try:
                # Take screenshot using improved method
                screenshot_path = f"screenshot_{port_number}.png"
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                        print(f"Using adb from PATH for screenshot")
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Check if device is connected
                print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"دستگاه 127.0.0.1:{port_number} متصل نیست")

                # Take screenshot using improved method (same as other screenshot functions)
                print(f"Taking screenshot for port {port_number}...")

                # Method 1: Try screencap to file first
                screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
                screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
                print(f"Screencap result: {screencap_result.stdout}")

                # Pull the file
                pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png {screenshot_path}'
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
                print(f"Pull result: {pull_result.stdout}")

                # Check if screenshot file exists
                if not os.path.exists(screenshot_path):
                    raise Exception(f"فایل screenshot ایجاد نشد: {screenshot_path}")

                print(f"Screenshot saved to {screenshot_path}")

                # Detect and click Enter button
                result = self.detect_and_click_enter_button(port_id, port_number, screenshot_path)

                def update_ui():
                    progress_window.destroy()
                    if result:
                        messagebox.showinfo("موفقیت", f"دکمه Enter در پورت {port_number} تشخیص داده شد و کلیک شد.")
                    else:
                        messagebox.showinfo("اطلاع", f"دکمه Enter در پورت {port_number} تشخیص داده نشد.")

                self.after(0, update_ui)

            except Exception as e:
                error_message = str(e)
                def show_error():
                    progress_window.destroy()
                    messagebox.showerror("خطا", f"خطا در تشخیص دکمه Enter: {error_message}")

                self.after(0, show_error)

        # Run detection in a separate thread
        import threading
        detection_thread = threading.Thread(target=perform_detection)
        detection_thread.daemon = True
        detection_thread.start()

    def detect_ad_speaker_for_selected_port(self):
        """Detect ad speaker icon for selected port and click it"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        values = item['values']

        if not values:
            messagebox.showwarning("هشدار", "اطلاعات پورت یافت نشد.")
            return

        port_number = values[1]  # Port number is in the second column

        # Get actual port number from database
        try:
            cursor = self.conn.cursor()

            # First try to find by exact match (if it's already a port number)
            if isinstance(port_number, str) and port_number.isdigit():
                cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (int(port_number),))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            # If it contains "MuMu", try to find by title
            elif isinstance(port_number, str) and "MuMu" in port_number:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    # Try to extract number and find by title pattern
                    try:
                        mumu_number = port_number.replace("MuMu", "").strip()
                        if mumu_number.isdigit():
                            cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {mumu_number}",))
                            result = cursor.fetchone()
                            if result:
                                port_id, actual_port_number = result
                                port_number = actual_port_number
                            else:
                                messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                                return
                        else:
                            messagebox.showerror("خطا", f"شماره پورت نامعتبر: {port_number}")
                            return
                    except:
                        messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {port_number}")
                        return

            # Try to find by title (in case it's a custom title)
            else:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            print(f"استفاده از پورت واقعی: {port_number} (ID: {port_id})")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("تشخیص ایکن بلندگو")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress label
        progress_label = ttk.Label(progress_window, text=f"در حال تشخیص ایکن بلندگو برای پورت {port_number}...")
        progress_label.pack(pady=20)

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        def perform_detection():
            try:
                # Take screenshot using improved method
                screenshot_path = f"screenshot_{port_number}.png"
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                        print(f"Using adb from PATH for screenshot")
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Check if device is connected
                print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"دستگاه 127.0.0.1:{port_number} متصل نیست")

                # Take screenshot using improved method (same as other screenshot functions)
                print(f"Taking screenshot for port {port_number}...")

                # Method 1: Try screencap to file first
                screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
                screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
                print(f"Screencap result: {screencap_result.stdout}")

                # Pull the file
                pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png {screenshot_path}'
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
                print(f"Pull result: {pull_result.stdout}")

                # Check if screenshot file exists
                if not os.path.exists(screenshot_path):
                    raise Exception(f"فایل screenshot ایجاد نشد: {screenshot_path}")

                print(f"Screenshot saved to {screenshot_path}")

                # Detect and click ad speaker icon
                result = self.detect_and_click_ad_speaker(port_id, port_number, screenshot_path)

                def update_ui():
                    progress_window.destroy()
                    if result:
                        messagebox.showinfo("موفقیت", f"ایکن بلندگو در پورت {port_number} تشخیص داده شد و کلیک شد.")
                    else:
                        messagebox.showinfo("اطلاع", f"ایکن بلندگو در پورت {port_number} تشخیص داده نشد.")

                self.after(0, update_ui)

            except Exception as e:
                error_message = str(e)
                def show_error():
                    progress_window.destroy()
                    messagebox.showerror("خطا", f"خطا در تشخیص ایکن بلندگو: {error_message}")

                self.after(0, show_error)

        # Run detection in a separate thread
        import threading
        detection_thread = threading.Thread(target=perform_detection)
        detection_thread.daemon = True
        detection_thread.start()

    def show_ad_speaker_crop(self):
        """Show cropped image of ad speaker area for selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        values = item['values']

        if not values:
            messagebox.showwarning("هشدار", "اطلاعات پورت یافت نشد.")
            return

        port_number = values[1]  # Port number is in the second column

        # Get actual port number from database
        try:
            cursor = self.conn.cursor()

            # First try to find by exact match (if it's already a port number)
            if isinstance(port_number, str) and port_number.isdigit():
                cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (int(port_number),))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            # If it contains "MuMu", try to find by title
            elif isinstance(port_number, str) and "MuMu" in port_number:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    # Try to extract number and find by title pattern
                    try:
                        mumu_number = port_number.replace("MuMu", "").strip()
                        if mumu_number.isdigit():
                            cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {mumu_number}",))
                            result = cursor.fetchone()
                            if result:
                                port_id, actual_port_number = result
                                port_number = actual_port_number
                            else:
                                messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                                return
                        else:
                            messagebox.showerror("خطا", f"شماره پورت نامعتبر: {port_number}")
                            return
                    except:
                        messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {port_number}")
                        return

            # Try to find by title (in case it's a custom title)
            else:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            print(f"استفاده از پورت واقعی: {port_number} (ID: {port_id})")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("گرفتن کراپ ایکن بلندگو")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress label
        progress_label = ttk.Label(progress_window, text=f"در حال گرفتن کراپ ایکن بلندگو برای پورت {port_number}...")
        progress_label.pack(pady=20)

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        def perform_crop():
            try:
                # Take screenshot using improved method
                screenshot_path = f"screenshot_{port_number}.png"
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                        print(f"Using adb from PATH for screenshot")
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Check if device is connected
                print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"دستگاه 127.0.0.1:{port_number} متصل نیست")

                # Take screenshot using improved method (same as other screenshot functions)
                print(f"Taking screenshot for port {port_number}...")

                # Method 1: Try screencap to file first
                screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
                screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
                print(f"Screencap result: {screencap_result.stdout}")

                # Pull the file
                pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png {screenshot_path}'
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
                print(f"Pull result: {pull_result.stdout}")

                # Check if screenshot file exists
                if not os.path.exists(screenshot_path):
                    raise Exception(f"فایل screenshot ایجاد نشد: {screenshot_path}")

                print(f"Screenshot saved to {screenshot_path}")

                # Create cropped image of speaker area
                from PIL import Image, ImageTk
                import numpy as np

                # Load the screenshot
                image = Image.open(screenshot_path)
                width, height = image.size

                print(f"🔊 ایجاد کراپ ایکن بلندگو برای تصویر {width}x{height}")

                # Create multiple crops to show different areas
                crops = []

                # 1. Bottom area crop (where speaker should be)
                bottom_crop = image.crop((0, int(height * 0.75), width, height))
                crops.append(("Bottom 25% (ناحیه کلی)", bottom_crop))

                # 2. Left bottom crop (where speaker icon is)
                left_bottom_crop = image.crop((0, int(height * 0.85), int(width * 0.4), height))
                crops.append(("Left Bottom (سمت چپ پایین)", left_bottom_crop))

                # 3. Speaker area crop (exact area around speaker)
                speaker_x = int(width * 0.05)  # 5% from left
                speaker_y = int(height * 0.88)  # 88% from top
                speaker_w = int(width * 0.25)   # 25% width
                speaker_h = int(height * 0.1)   # 10% height
                speaker_crop = image.crop((speaker_x, speaker_y, speaker_x + speaker_w, speaker_y + speaker_h))
                crops.append(("Speaker Area (ناحیه بلندگو)", speaker_crop))

                # 4. Exact position crop (around click position)
                exact_x = int(230 * width / 540)  # Scale to current resolution (corrected from 80 to 230)
                exact_y = int(880 * height / 960)  # Scale to current resolution
                crop_size = 100  # 100x100 pixel crop
                exact_crop = image.crop((
                    max(0, exact_x - crop_size//2),
                    max(0, exact_y - crop_size//2),
                    min(width, exact_x + crop_size//2),
                    min(height, exact_y + crop_size//2)
                ))
                crops.append(("Exact Click Position (موقعیت کلیک)", exact_crop))

                # Save all crops
                crop_paths = []
                for i, (name, crop_img) in enumerate(crops):
                    crop_path = f"speaker_crop_{port_number}_{i+1}_{name.split('(')[0].strip().replace(' ', '_')}.png"
                    crop_img.save(crop_path)
                    crop_paths.append((name, crop_path))
                    print(f"کراپ ذخیره شد: {crop_path}")

                def update_ui():
                    progress_window.destroy()

                    # Show all crops in a new window
                    crop_window = tk.Toplevel(self)
                    crop_window.title(f"کراپ ایکن بلندگو - پورت {port_number}")
                    crop_window.geometry("800x600")

                    # Create scrollable frame
                    canvas = tk.Canvas(crop_window)
                    scrollbar = ttk.Scrollbar(crop_window, orient="vertical", command=canvas.yview)
                    scrollable_frame = ttk.Frame(canvas)

                    scrollable_frame.bind(
                        "<Configure>",
                        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
                    )

                    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
                    canvas.configure(yscrollcommand=scrollbar.set)

                    # Add crops to the frame
                    for i, (name, crop_path) in enumerate(crop_paths):
                        # Add label
                        label = ttk.Label(scrollable_frame, text=f"{i+1}. {name}", font=("Arial", 12, "bold"))
                        label.pack(pady=(10, 5))

                        # Add image
                        try:
                            crop_img = Image.open(crop_path)
                            # Resize if too large
                            if crop_img.width > 600:
                                ratio = 600 / crop_img.width
                                new_size = (600, int(crop_img.height * ratio))
                                crop_img = crop_img.resize(new_size, Image.Resampling.LANCZOS)

                            photo = ImageTk.PhotoImage(crop_img)
                            img_label = ttk.Label(scrollable_frame, image=photo)
                            img_label.image = photo  # Keep a reference
                            img_label.pack(pady=5)

                            # Add path label
                            path_label = ttk.Label(scrollable_frame, text=f"فایل: {crop_path}", font=("Arial", 8))
                            path_label.pack(pady=(0, 10))

                        except Exception as e:
                            error_label = ttk.Label(scrollable_frame, text=f"خطا در نمایش تصویر: {e}")
                            error_label.pack(pady=5)

                    canvas.pack(side="left", fill="both", expand=True)
                    scrollbar.pack(side="right", fill="y")

                self.after(0, update_ui)

            except Exception as e:
                error_message = str(e)
                def show_error():
                    progress_window.destroy()
                    messagebox.showerror("خطا", f"خطا در گرفتن کراپ ایکن بلندگو: {error_message}")

                self.after(0, show_error)

        # Run crop in a separate thread
        import threading
        crop_thread = threading.Thread(target=perform_crop)
        crop_thread.daemon = True
        crop_thread.start()

    def show_enter_button_crop(self):
        """Show cropped image of Enter button area for selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        values = item['values']

        if not values:
            messagebox.showwarning("هشدار", "اطلاعات پورت یافت نشد.")
            return

        port_number = values[1]  # Port number is in the second column

        # Get actual port number from database
        try:
            cursor = self.conn.cursor()

            # First try to find by exact match (if it's already a port number)
            if isinstance(port_number, str) and port_number.isdigit():
                cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (int(port_number),))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            # If it contains "MuMu", try to find by title
            elif isinstance(port_number, str) and "MuMu" in port_number:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    # Try to extract number and find by title pattern
                    try:
                        mumu_number = port_number.replace("MuMu", "").strip()
                        if mumu_number.isdigit():
                            cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {mumu_number}",))
                            result = cursor.fetchone()
                            if result:
                                port_id, actual_port_number = result
                                port_number = actual_port_number
                            else:
                                messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                                return
                        else:
                            messagebox.showerror("خطا", f"شماره پورت نامعتبر: {port_number}")
                            return
                    except:
                        messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {port_number}")
                        return

            # Try to find by title (in case it's a custom title)
            else:
                cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_number,))
                result = cursor.fetchone()
                if result:
                    port_id, actual_port_number = result
                    port_number = actual_port_number
                else:
                    messagebox.showerror("خطا", f"پورت {port_number} در پایگاه داده یافت نشد.")
                    return

            print(f"استفاده از پورت واقعی: {port_number} (ID: {port_id})")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در دریافت اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("گرفتن کراپ دکمه Enter")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress label
        progress_label = ttk.Label(progress_window, text=f"در حال گرفتن کراپ دکمه Enter برای پورت {port_number}...")
        progress_label.pack(pady=20)

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar.start()

        def perform_crop():
            try:
                # Take screenshot using improved method
                screenshot_path = f"screenshot_{port_number}.png"
                adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

                # Check if adb exists
                if not os.path.exists(adb_path):
                    try:
                        adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                        adb_path = "adb"
                        print(f"Using adb from PATH for screenshot")
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise Exception("adb.exe یافت نشد")

                # Check if device is connected
                print(f"Checking if device 127.0.0.1:{port_number} is connected for screenshot...")
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"دستگاه 127.0.0.1:{port_number} متصل نیست")

                # Take screenshot using improved method (same as other screenshot functions)
                print(f"Taking screenshot for port {port_number}...")

                # Method 1: Try screencap to file first
                screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell screencap -p /sdcard/screen.png'
                screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True, text=True)
                print(f"Screencap result: {screencap_result.stdout}")

                # Pull the file
                pull_cmd = f'{adb_path} -s 127.0.0.1:{port_number} pull /sdcard/screen.png {screenshot_path}'
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True)
                print(f"Pull result: {pull_result.stdout}")

                # Check if screenshot file exists
                if not os.path.exists(screenshot_path):
                    raise Exception(f"فایل screenshot ایجاد نشد: {screenshot_path}")

                print(f"Screenshot saved to {screenshot_path}")

                # Create cropped image of Enter button area
                from PIL import Image, ImageTk
                import numpy as np

                # Load the screenshot
                image = Image.open(screenshot_path)

                # Convert to RGB if needed (fix for RGBA images)
                if image.mode != 'RGB':
                    image = image.convert('RGB')

                width, height = image.size

                print(f"🔘 ایجاد کراپ دکمه Enter برای تصویر {width}x{height} (mode: {image.mode})")

                # Create multiple crops to show different areas
                crops = []

                # 1. Bottom area crop (where Enter button should be)
                bottom_crop = image.crop((0, int(height * 0.6), width, height))
                crops.append(("Bottom 40% (ناحیه کلی)", bottom_crop))

                # 2. Center bottom crop (where Enter button usually is)
                center_bottom_crop = image.crop((int(width * 0.2), int(height * 0.7), int(width * 0.8), height))
                crops.append(("Center Bottom (مرکز پایین)", center_bottom_crop))

                # 3. Detect blue areas and show them
                img_array = np.array(image)

                # Try multiple blue color ranges
                blue_ranges = [
                    ([100, 180, 220], [180, 255, 255], "Bright Blue"),
                    ([80, 160, 200], [160, 240, 255], "Cyan Blue"),
                    ([60, 140, 180], [200, 255, 255], "Broad Blue")
                ]

                for i, (lower, upper, name) in enumerate(blue_ranges):
                    blue_lower = np.array(lower)
                    blue_upper = np.array(upper)
                    blue_mask = np.all((img_array >= blue_lower) & (img_array <= blue_upper), axis=2)
                    blue_pixels = np.sum(blue_mask)

                    if blue_pixels > 30:  # If enough blue pixels found
                        blue_positions = np.where(blue_mask)
                        if len(blue_positions[0]) > 0:
                            center_y = int(np.mean(blue_positions[0]))
                            center_x = int(np.mean(blue_positions[1]))

                            # Create crop around detected blue area
                            margin = 100
                            x_start = max(0, center_x - margin)
                            y_start = max(0, center_y - margin)
                            x_end = min(width, center_x + margin)
                            y_end = min(height, center_y + margin)

                            blue_crop = image.crop((x_start, y_start, x_end, y_end))
                            crops.append((f"Detected {name} Area (ناحیه آبی تشخیص داده شده)", blue_crop))

                            # Also create a small exact crop around the center
                            exact_margin = 50
                            exact_x_start = max(0, center_x - exact_margin)
                            exact_y_start = max(0, center_y - exact_margin)
                            exact_x_end = min(width, center_x + exact_margin)
                            exact_y_end = min(height, center_y + exact_margin)

                            exact_crop = image.crop((exact_x_start, exact_y_start, exact_x_end, exact_y_end))
                            crops.append((f"Exact Click Position {name} (موقعیت کلیک دقیق)", exact_crop))

                            print(f"Blue area detected with {name}: center=({center_x}, {center_y}), pixels={blue_pixels}")
                            break

                # Save all crops
                crop_paths = []
                for i, (name, crop_img) in enumerate(crops):
                    crop_path = f"enter_crop_{port_number}_{i+1}_{name.split('(')[0].strip().replace(' ', '_')}.png"
                    crop_img.save(crop_path)
                    crop_paths.append((name, crop_path))
                    print(f"کراپ ذخیره شد: {crop_path}")

                def update_ui():
                    progress_window.destroy()

                    # Show all crops in a new window
                    crop_window = tk.Toplevel(self)
                    crop_window.title(f"کراپ دکمه Enter - پورت {port_number}")
                    crop_window.geometry("800x600")

                    # Create scrollable frame
                    canvas = tk.Canvas(crop_window)
                    scrollbar = ttk.Scrollbar(crop_window, orient="vertical", command=canvas.yview)
                    scrollable_frame = ttk.Frame(canvas)

                    scrollable_frame.bind(
                        "<Configure>",
                        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
                    )

                    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
                    canvas.configure(yscrollcommand=scrollbar.set)

                    # Add crops to the frame
                    for i, (name, crop_path) in enumerate(crop_paths):
                        # Add label
                        label = ttk.Label(scrollable_frame, text=f"{i+1}. {name}", font=("Arial", 12, "bold"))
                        label.pack(pady=(10, 5))

                        # Add image
                        try:
                            crop_img = Image.open(crop_path)
                            # Resize if too large
                            if crop_img.width > 600:
                                ratio = 600 / crop_img.width
                                new_size = (600, int(crop_img.height * ratio))
                                crop_img = crop_img.resize(new_size, Image.Resampling.LANCZOS)

                            photo = ImageTk.PhotoImage(crop_img)
                            img_label = ttk.Label(scrollable_frame, image=photo)
                            img_label.image = photo  # Keep a reference
                            img_label.pack(pady=5)

                            # Add path label
                            path_label = ttk.Label(scrollable_frame, text=f"فایل: {crop_path}", font=("Arial", 8))
                            path_label.pack(pady=(0, 10))

                        except Exception as e:
                            error_label = ttk.Label(scrollable_frame, text=f"خطا در نمایش تصویر: {e}")
                            error_label.pack(pady=5)

                    canvas.pack(side="left", fill="both", expand=True)
                    scrollbar.pack(side="right", fill="y")

                self.after(0, update_ui)

            except Exception as e:
                error_message = str(e)
                def show_error():
                    progress_window.destroy()
                    messagebox.showerror("خطا", f"خطا در گرفتن کراپ دکمه Enter: {error_message}")

                self.after(0, show_error)

        # Run crop in a separate thread
        import threading
        crop_thread = threading.Thread(target=perform_crop)
        crop_thread.daemon = True
        crop_thread.start()

    def detect_loading_for_selected_port(self):
        """Detect loading screen for selected port and close KKT if found"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        port_display = item['values'][1]

        # Extract numeric port number if it contains "MuMu" prefix
        if isinstance(port_display, str) and "MuMu" in port_display:
            try:
                # Extract number from "MuMu 30" format
                port_display = port_display.replace("MuMu", "").strip()
                if not port_display.isdigit():
                    messagebox.showerror("خطا", f"شماره پورت نامعتبر: {item['values'][1]}")
                    return
            except:
                messagebox.showerror("خطا", f"نمی‌توان شماره پورت را استخراج کرد: {item['values'][1]}")
                return

        # Find the actual port number and port ID
        port_number = self.get_port_number_from_display(port_display)
        if not port_number:
            # If not found, use the display value directly if it's numeric
            try:
                port_number = int(port_display)
            except:
                messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
                return

        # Get port ID
        try:
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM ports WHERE port_number = ?", (port_number,))
            result = cursor.fetchone()
            conn.close()

            if result:
                port_id = result[0]
            else:
                messagebox.showwarning("هشدار", f"پورت {port_number} در دیتابیس یافت نشد.")
                return
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی اطلاعات پورت: {e}")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self)
        progress_window.title("تشخیص Loading")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()

        # Center the window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (progress_window.winfo_width() // 2)
        y = (progress_window.winfo_screenheight() // 2) - (progress_window.winfo_height() // 2)
        progress_window.geometry(f"+{x}+{y}")

        # Add progress elements
        ttk.Label(progress_window, text=f"در حال تشخیص Loading برای پورت {port_number}...").pack(pady=20)

        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100)
        progress_bar.pack(pady=10, padx=20, fill='x')

        def run_detection():
            try:
                # Update progress
                progress_var.set(20)
                progress_window.update()

                # Take screenshot
                screenshot_path = self.take_port_screenshot(port_id, port_number)

                # Update progress
                progress_var.set(60)
                progress_window.update()

                # Try to detect loading screen
                is_loading = self.detect_loading_screen(screenshot_path)

                # Update progress
                progress_var.set(100)
                progress_window.update()

                # Close progress window
                progress_window.destroy()

                # Show result and take action
                if is_loading:
                    result = messagebox.askyesno("Loading تشخیص داده شد",
                                               f"صفحه Loading در پورت {port_number} تشخیص داده شد.\n\n"
                                               f"آیا می‌خواهید برنامه KKT را ببندید؟")
                    if result:
                        try:
                            self.close_kkt_app_for_port(port_id, port_number)
                            messagebox.showinfo("موفقیت", f"برنامه KKT در پورت {port_number} بسته شد.")
                        except Exception as e:
                            messagebox.showerror("خطا", f"خطا در بستن KKT: {e}")
                else:
                    messagebox.showinfo("نتیجه تشخیص", f"صفحه Loading در پورت {port_number} تشخیص داده نشد.")

            except Exception as e:
                progress_window.destroy()
                messagebox.showerror("خطا", f"خطا در تشخیص Loading: {e}")

        # Start detection in a thread
        threading.Thread(target=run_detection, daemon=True).start()

    def close_kkt_app_for_port(self, port_id, port_number):
        """Close KKT app in the specified port"""
        try:
            # Try to use adb directly without finding the window first
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists in the specified path
            if not os.path.exists(adb_path):
                # Try to find adb in PATH
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    # If we get here, adb is in PATH
                    adb_path = "adb"
                    print(f"Using adb from PATH")
                except (subprocess.SubprocessError, FileNotFoundError):
                    raise Exception("adb.exe یافت نشد. لطفاً مطمئن شوید که adb در مسیر برنامه یا PATH وجود دارد.")

            # Check if device is connected
            print(f"Checking if device 127.0.0.1:{port_number} is connected...")
            devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
            print(f"Connected devices: {devices_result.stdout}")

            if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                # Try to connect to the device
                print(f"Trying to connect to 127.0.0.1:{port_number}...")
                connect_cmd = f'{adb_path} connect 127.0.0.1:{port_number}'
                connect_result = subprocess.run(connect_cmd, shell=True, capture_output=True, text=True)
                print(f"Connect result: {connect_result.stdout}")

                # Check again if connected
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices after connect attempt: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"اتصال به دستگاه با پورت {port_number} امکان‌پذیر نیست.")

            # Try a simpler approach to check if KKT app is running
            print(f"Checking if KKT app is running...")
            try:
                # List all packages and grep for KKT
                check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "ps | grep com.KepithorStudios.KKTFaucet"'
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
                output = result.stdout.strip()

                print(f"Check if KKT is running result: '{output}'")

                # If app is running (output from grep), close it
                if output:
                    print(f"KKT app is running, closing it...")
                    # Close the KKT app
                    close_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell am force-stop com.KepithorStudios.KKTFaucet'
                    close_result = subprocess.run(close_cmd, shell=True, capture_output=True, text=True)
                    print(f"Close app result: {close_result.stdout}")

                    # Verify that the app is closed
                    time.sleep(1)  # Wait a bit for the app to close
                    check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "ps | grep com.KepithorStudios.KKTFaucet"'
                    result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
                    output = result.stdout.strip()

                    if not output:
                        print(f"KKT app was successfully closed")
                        return True
                    else:
                        print(f"KKT app is still running after close attempt")
                        return False
                else:
                    print(f"KKT app is not running")
                    # App is not running
                    return False
            except Exception as e:
                print(f"Error in simplified approach, falling back to original method: {e}")
                # Fall back to original method
                check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "dumpsys window windows | grep -E \'mCurrentFocus\' | grep -q com.KepithorStudios.KKTFaucet && echo running || echo not_running"'
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
                output = result.stdout.strip()

                print(f"Original check method result: '{output}'")

                if "running" in output:
                    # Close the KKT app
                    close_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell am force-stop com.KepithorStudios.KKTFaucet'
                    subprocess.run(close_cmd, shell=True)
                    return True
                else:
                    # App is not running
                    return False

        except Exception as e:
            print(f"Error in close_kkt_app_for_port: {e}")
            raise Exception(f"خطا در بستن برنامه KKT: {e}")

    def close_kkt_app(self):
        """Close KKT app in the selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        port_display = item['values'][1]

        # Find the actual port number and port ID
        port_number = self.get_port_number_from_display(port_display)
        if not port_number:
            messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
            return

        # Get port ID
        try:
            self.cursor.execute("SELECT id FROM ports WHERE port_number = ?", (port_number,))
            result = self.cursor.fetchone()
            if not result:
                messagebox.showwarning("هشدار", f"پورت {port_number} در دیتابیس یافت نشد.")
                return

            port_id = result[0]

            # Show a progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("در حال بستن KKT")
            progress_window.geometry("300x100")
            progress_window.transient(self)
            progress_window.grab_set()

            # Add a label
            status_label = ttk.Label(progress_window, text=f"در حال بستن KKT در پورت {port_number}...")
            status_label.pack(pady=10)

            # Add a progress bar
            progress_var = tk.IntVar()
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100)
            progress_bar.pack(fill='x', padx=10, pady=10)

            # Update progress
            progress_var.set(10)
            progress_window.update()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی اطلاعات پورت: {e}")
            return

        try:
            # Update progress
            progress_var.set(30)
            status_label.config(text=f"در حال بررسی وضعیت پورت {port_number}...")
            progress_window.update()

            # Try to close KKT app using the improved function
            try:
                result = self.close_kkt_app_for_port(port_id, port_number)

                # Update progress
                progress_var.set(90)
                progress_window.update()

                # Close progress window
                progress_window.destroy()

                if result:
                    messagebox.showinfo("اطلاعات", f"برنامه KKT در پورت {port_number} با موفقیت بسته شد.")
                else:
                    messagebox.showinfo("اطلاعات", f"برنامه KKT در پورت {port_number} در حال اجرا نیست.")
            except Exception as e:
                # Close progress window
                progress_window.destroy()
                messagebox.showerror("خطا", f"خطا در بستن برنامه KKT: {e}")

        except Exception as e:
            # Make sure progress window is closed
            try:
                progress_window.destroy()
            except:
                pass
            messagebox.showerror("خطا", f"خطا در بستن برنامه KKT: {e}")

    def get_current_focused_app(self, port_number, log_callback=None):
        """Get the currently focused/visible app on the emulator screen"""
        def log(message):
            """Helper function to log messages"""
            print(f"[FOCUS-{port_number}] {message}")
            if log_callback:
                log_callback(message)

        try:
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")
            if not os.path.exists(adb_path):
                adb_path = "adb"

            log("تشخیص برنامه فعال روی صفحه...")

            # Method 1: Get current focused window using dumpsys window
            try:
                focus_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "dumpsys window windows | grep -E \'mCurrentFocus|mFocusedApp\'"'
                focus_result = subprocess.run(focus_cmd, shell=True, capture_output=True, text=True, timeout=10)
                focus_output = focus_result.stdout.strip()
                log(f"خروجی تشخیص فوکوس: {focus_output}")

                if focus_output:
                    # Parse the focused app package name
                    import re
                    # Look for package names in the output
                    package_patterns = [
                        r'mCurrentFocus=.*?([a-zA-Z][a-zA-Z0-9_]*\.[a-zA-Z][a-zA-Z0-9_]*(?:\.[a-zA-Z][a-zA-Z0-9_]*)*)',
                        r'mFocusedApp=.*?([a-zA-Z][a-zA-Z0-9_]*\.[a-zA-Z][a-zA-Z0-9_]*(?:\.[a-zA-Z][a-zA-Z0-9_]*)*)'
                    ]

                    for pattern in package_patterns:
                        matches = re.findall(pattern, focus_output)
                        if matches:
                            focused_package = matches[0]
                            log(f"✅ برنامه فعال تشخیص داده شد: {focused_package}")
                            return self.identify_app_type(focused_package, log_callback)

            except Exception as e:
                log(f"خطا در روش اول: {e}")

            # Method 2: Get top activity using dumpsys activity
            try:
                activity_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "dumpsys activity activities | grep -E \'ResumedActivity|mResumedActivity\' | head -1"'
                activity_result = subprocess.run(activity_cmd, shell=True, capture_output=True, text=True, timeout=10)
                activity_output = activity_result.stdout.strip()
                log(f"خروجی فعالیت فعال: {activity_output}")

                if activity_output:
                    import re
                    # Look for package names in the activity output
                    package_match = re.search(r'([a-zA-Z][a-zA-Z0-9_]*\.[a-zA-Z][a-zA-Z0-9_]*(?:\.[a-zA-Z][a-zA-Z0-9_]*)*)', activity_output)
                    if package_match:
                        focused_package = package_match.group(1)
                        log(f"✅ برنامه فعال از طریق activity تشخیص داده شد: {focused_package}")
                        return self.identify_app_type(focused_package, log_callback)

            except Exception as e:
                log(f"خطا در روش دوم: {e}")

            # Method 3: Get running processes and try to identify the foreground one
            try:
                ps_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "ps | grep -v \'kernel\' | grep -v \'root\'"'
                ps_result = subprocess.run(ps_cmd, shell=True, capture_output=True, text=True, timeout=10)
                ps_output = ps_result.stdout.strip()

                # Look for common app packages in running processes
                common_apps = [
                    'com.KepithorStudios.KKTFaucet',
                    'com.android.chrome',
                    'com.android.browser',
                    'com.opera.browser',
                    'com.UCMobile.intl',
                    'com.android.launcher',
                    'com.miui.home'
                ]

                for app_package in common_apps:
                    if app_package in ps_output:
                        log(f"✅ برنامه {app_package} در حال اجرا تشخیص داده شد")
                        return self.identify_app_type(app_package, log_callback)

            except Exception as e:
                log(f"خطا در روش سوم: {e}")

            log("❌ نتوانست برنامه فعال را تشخیص دهد")
            return {"type": "unknown", "package": "unknown", "name": "نامشخص"}

        except Exception as e:
            log(f"❌ خطا در تشخیص برنامه فعال: {e}")
            return {"type": "unknown", "package": "unknown", "name": "نامشخص"}

    def identify_app_type(self, package_name, log_callback=None):
        """Identify the type of app based on package name"""
        def log(message):
            if log_callback:
                log_callback(message)

        app_types = {
            # KKT App
            'com.KepithorStudios.KKTFaucet': {
                'type': 'kkt',
                'name': 'KKT Faucet',
                'description': 'برنامه KKT'
            },

            # Browsers
            'com.android.chrome': {
                'type': 'browser',
                'name': 'Chrome',
                'description': 'مرورگر کروم'
            },
            'com.android.browser': {
                'type': 'browser',
                'name': 'Browser',
                'description': 'مرورگر پیش‌فرض'
            },
            'com.opera.browser': {
                'type': 'browser',
                'name': 'Opera',
                'description': 'مرورگر اپرا'
            },
            'com.UCMobile.intl': {
                'type': 'browser',
                'name': 'UC Browser',
                'description': 'مرورگر UC'
            },
            'org.mozilla.firefox': {
                'type': 'browser',
                'name': 'Firefox',
                'description': 'مرورگر فایرفاکس'
            },

            # Launchers/Home
            'com.android.launcher': {
                'type': 'launcher',
                'name': 'Launcher',
                'description': 'صفحه اصلی'
            },
            'com.miui.home': {
                'type': 'launcher',
                'name': 'MIUI Home',
                'description': 'صفحه اصلی MIUI'
            },
            'com.android.launcher3': {
                'type': 'launcher',
                'name': 'Launcher3',
                'description': 'صفحه اصلی'
            },

            # System apps
            'com.android.settings': {
                'type': 'system',
                'name': 'Settings',
                'description': 'تنظیمات سیستم'
            },
            'com.android.systemui': {
                'type': 'system',
                'name': 'System UI',
                'description': 'رابط کاربری سیستم'
            }
        }

        if package_name in app_types:
            app_info = app_types[package_name].copy()
            app_info['package'] = package_name
            log(f"✅ نوع برنامه تشخیص داده شد: {app_info['description']} ({package_name})")
            return app_info
        else:
            # Try to guess based on package name patterns
            if 'browser' in package_name.lower() or 'chrome' in package_name.lower():
                app_type = 'browser'
                description = 'مرورگر'
            elif 'launcher' in package_name.lower() or 'home' in package_name.lower():
                app_type = 'launcher'
                description = 'صفحه اصلی'
            elif 'game' in package_name.lower():
                app_type = 'game'
                description = 'بازی'
            else:
                app_type = 'other'
                description = 'برنامه دیگر'

            app_info = {
                'type': app_type,
                'package': package_name,
                'name': package_name.split('.')[-1],
                'description': description
            }
            log(f"🔍 نوع برنامه حدس زده شد: {description} ({package_name})")
            return app_info

    def open_kkt_app_with_logging(self, port_id, port_number, log_callback=None):
        """Open KKT app in the specified port with detailed logging"""
        def log(message):
            """Helper function to log messages"""
            print(f"[KKT-{port_number}] {message}")
            if log_callback:
                log_callback(message)

        # Get the port's window title using thread-safe connection
        conn = sqlite3.connect('ports.db')
        cursor = conn.cursor()
        cursor.execute("SELECT title FROM ports WHERE id = ?", (port_id,))
        result = cursor.fetchone()
        conn.close()
        window_title = result[0] if result and result[0] else f"MuMu {port_number}"

        log(f"شروع باز کردن KKT برای پورت {port_number} (عنوان: {window_title})")

        try:
            # Try to use adb directly without finding the window first
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")
            log(f"بررسی مسیر adb: {adb_path}")

            # Check if adb exists in the specified path
            if not os.path.exists(adb_path):
                log("adb در مسیر محلی یافت نشد، بررسی PATH...")
                # Try to find adb in PATH
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True, timeout=10)
                    # If we get here, adb is in PATH
                    adb_path = "adb"
                    log(f"adb در PATH یافت شد: {adb_version.stdout.strip()}")
                except (subprocess.SubprocessError, FileNotFoundError, subprocess.TimeoutExpired):
                    log("❌ adb یافت نشد")
                    raise Exception("adb.exe یافت نشد. لطفاً مطمئن شوید که adb در مسیر برنامه یا PATH وجود دارد.")
            else:
                log("✅ adb در مسیر محلی یافت شد")

            # Check if adb server is running
            try:
                log("بررسی وضعیت سرور adb...")
                devices_check = subprocess.run([adb_path, "devices"], check=True, capture_output=True, text=True, timeout=10)
                log("✅ سرور adb در حال اجرا است")
            except (subprocess.SubprocessError, FileNotFoundError, subprocess.TimeoutExpired):
                # Start adb server if not running
                try:
                    log("شروع سرور adb...")
                    subprocess.run([adb_path, "start-server"], check=True, capture_output=True, text=True, timeout=15)
                    log("✅ سرور adb شروع شد")
                except Exception as e:
                    log(f"❌ خطا در شروع سرور adb: {e}")
                    raise Exception(f"خطا در اجرای adb: {e}")

            # Check if device is connected
            log(f"بررسی اتصال دستگاه 127.0.0.1:{port_number}...")
            devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True, timeout=10)
            log(f"دستگاه‌های متصل: {devices_result.stdout.strip()}")

            if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                # Try to connect to the device
                log(f"تلاش برای اتصال به 127.0.0.1:{port_number}...")
                connect_cmd = f'{adb_path} connect 127.0.0.1:{port_number}'
                connect_result = subprocess.run(connect_cmd, shell=True, capture_output=True, text=True, timeout=15)
                log(f"نتیجه اتصال: {connect_result.stdout.strip()}")

                # Check again if connected
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True, timeout=10)
                log(f"دستگاه‌های متصل پس از تلاش اتصال: {devices_result.stdout.strip()}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    log("❌ اتصال به دستگاه ناموفق")
                    raise Exception(f"اتصال به دستگاه با پورت {port_number} امکان‌پذیر نیست.")
            else:
                log("✅ دستگاه قبلاً متصل است")

            # First check if KKT app is installed
            log("بررسی نصب برنامه KKT...")
            try:
                # Check if KKT package is installed
                package_check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "pm list packages | grep com.KepithorStudios.KKTFaucet"'
                package_result = subprocess.run(package_check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                package_output = package_result.stdout.strip()
                log(f"نتیجه بررسی نصب KKT: '{package_output}'")

                if not package_output:
                    log("❌ برنامه KKT نصب نیست!")
                    raise Exception("برنامه KKT در این دستگاه نصب نیست.")
                else:
                    log("✅ برنامه KKT نصب است")
            except Exception as e:
                log(f"خطا در بررسی نصب KKT: {e}")

            # Try a simpler approach to check if KKT app is running
            log("بررسی وضعیت اجرای برنامه KKT...")
            try:
                # List all packages and grep for KKT
                check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "ps | grep com.KepithorStudios.KKTFaucet"'
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                output = result.stdout.strip()

                log(f"نتیجه بررسی اجرای KKT: '{output}'")

                # If app is not running (no output from grep), start it
                if not output:
                    log("برنامه KKT در حال اجرا نیست، شروع برنامه...")

                    # Method 1: Try am start with MainActivity
                    log("روش 1: تلاش با am start MainActivity...")
                    start_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell am start -n com.KepithorStudios.KKTFaucet/.MainActivity'
                    start_result = subprocess.run(start_cmd, shell=True, capture_output=True, text=True, timeout=15)
                    log(f"نتیجه شروع برنامه (am start MainActivity): {start_result.stdout.strip()}")

                    # Wait and check if app started
                    time.sleep(2)
                    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if check_result.stdout.strip():
                        log("✅ برنامه KKT با روش 1 شروع شد")
                        return True

                    # Method 2: Try am start with different activity
                    log("روش 2: تلاش با am start بدون activity مشخص...")
                    start_cmd2 = f'{adb_path} -s 127.0.0.1:{port_number} shell am start com.KepithorStudios.KKTFaucet'
                    start_result2 = subprocess.run(start_cmd2, shell=True, capture_output=True, text=True, timeout=15)
                    log(f"نتیجه شروع برنامه (am start package): {start_result2.stdout.strip()}")

                    # Wait and check if app started
                    time.sleep(2)
                    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if check_result.stdout.strip():
                        log("✅ برنامه KKT با روش 2 شروع شد")
                        return True

                    # Method 3: Try monkey
                    log("روش 3: تلاش با monkey...")
                    monkey_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell monkey -p com.KepithorStudios.KKTFaucet -c android.intent.category.LAUNCHER 1'
                    monkey_result = subprocess.run(monkey_cmd, shell=True, capture_output=True, text=True, timeout=15)
                    log(f"نتیجه شروع برنامه (monkey): {monkey_result.stdout.strip()}")

                    # Wait and check if app started
                    time.sleep(2)
                    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if check_result.stdout.strip():
                        log("✅ برنامه KKT با روش 3 شروع شد")
                        return True

                    # Method 4: Try input tap on app icon (if we can find it)
                    log("روش 4: تلاش با input tap...")
                    # This would require knowing the coordinates of the app icon
                    # For now, we'll skip this method

                    log("❌ هیچ روشی برای باز کردن برنامه موفق نبود")
                    log("💡 لطفاً بررسی کنید که برنامه KKT در دستگاه نصب و قابل اجرا است")
                    return False
                else:
                    log("✅ برنامه KKT قبلاً در حال اجرا است")
                    return False  # Already running
            except Exception as e:
                log(f"خطا در روش ساده، استفاده از روش اصلی: {e}")
                # Fall back to original method
                check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "dumpsys window windows | grep -E \'mCurrentFocus\' | grep -q com.KepithorStudios.KKTFaucet && echo running || echo not_running"'
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=15)
                output = result.stdout.strip()

                log(f"نتیجه روش اصلی: '{output}'")

                # If app is not running, start it
                if "not_running" in output or not output:
                    log("شروع برنامه KKT با monkey...")
                    # Start the KKT app
                    start_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell monkey -p com.KepithorStudios.KKTFaucet -c android.intent.category.LAUNCHER 1'
                    subprocess.run(start_cmd, shell=True, timeout=15)
                    log("انتظار 2 ثانیه برای شروع برنامه...")
                    time.sleep(2)
                    log("✅ برنامه KKT با موفقیت شروع شد")
                    return True
                else:
                    log("✅ برنامه KKT قبلاً در حال اجرا است")
                    return False  # Already running

        except Exception as e:
            log(f"❌ خطا در باز کردن KKT: {e}")
            raise Exception(f"خطا در باز کردن برنامه KKT: {e}")

    def open_kkt_app(self, port_id, port_number):
        """Open KKT app in the specified port (original function for backward compatibility)"""
        return self.open_kkt_app_with_logging(port_id, port_number)

    def open_kkt_app_original(self, port_id, port_number):
        """Original open KKT app function (kept for reference)"""
        # Get the port's window title using thread-safe connection
        conn = sqlite3.connect('ports.db')
        cursor = conn.cursor()
        cursor.execute("SELECT title FROM ports WHERE id = ?", (port_id,))
        result = cursor.fetchone()
        conn.close()
        window_title = result[0] if result and result[0] else f"MuMu {port_number}"

        try:
            # Try to use adb directly without finding the window first
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists in the specified path
            if not os.path.exists(adb_path):
                # Try to find adb in PATH
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    # If we get here, adb is in PATH
                    adb_path = "adb"
                    print(f"Using adb from PATH")
                except (subprocess.SubprocessError, FileNotFoundError):
                    raise Exception("adb.exe یافت نشد. لطفاً مطمئن شوید که adb در مسیر برنامه یا PATH وجود دارد.")

            # Check if adb server is running
            try:
                print(f"Checking if adb server is running...")
                subprocess.run([adb_path, "devices"], check=True, capture_output=True, text=True)
            except (subprocess.SubprocessError, FileNotFoundError):
                # Start adb server if not running
                try:
                    print(f"Starting adb server...")
                    subprocess.run([adb_path, "start-server"], check=True, capture_output=True, text=True)
                except Exception as e:
                    print(f"Error starting adb server: {e}")
                    raise Exception(f"خطا در اجرای adb: {e}")

            # Check if device is connected
            print(f"Checking if device 127.0.0.1:{port_number} is connected...")
            devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
            print(f"Connected devices: {devices_result.stdout}")

            if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                # Try to connect to the device
                print(f"Trying to connect to 127.0.0.1:{port_number}...")
                connect_cmd = f'{adb_path} connect 127.0.0.1:{port_number}'
                connect_result = subprocess.run(connect_cmd, shell=True, capture_output=True, text=True)
                print(f"Connect result: {connect_result.stdout}")

                # Check again if connected
                devices_result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
                print(f"Connected devices after connect attempt: {devices_result.stdout}")

                if f"127.0.0.1:{port_number}" not in devices_result.stdout:
                    raise Exception(f"اتصال به دستگاه با پورت {port_number} امکان‌پذیر نیست.")

            # Try a simpler approach to check if KKT app is running
            print(f"Checking if KKT app is running...")
            try:
                # List all packages and grep for KKT
                check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "ps | grep com.KepithorStudios.KKTFaucet"'
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
                output = result.stdout.strip()

                print(f"Check if KKT is running result: '{output}'")

                # If app is not running (no output from grep), start it
                if not output:
                    print(f"KKT app is not running, starting it...")
                    # Start the KKT app with am start
                    start_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell am start -n com.KepithorStudios.KKTFaucet/.MainActivity'
                    start_result = subprocess.run(start_cmd, shell=True, capture_output=True, text=True)
                    print(f"Start app result: {start_result.stdout}")

                    # If am start fails, try monkey as fallback
                    if "Error" in start_result.stdout or "Exception" in start_result.stdout:
                        print(f"am start failed, trying monkey...")
                        monkey_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell monkey -p com.KepithorStudios.KKTFaucet -c android.intent.category.LAUNCHER 1'
                        monkey_result = subprocess.run(monkey_cmd, shell=True, capture_output=True, text=True)
                        print(f"Monkey start result: {monkey_result.stdout}")

                    time.sleep(3)  # Wait for app to start
                    return True
                else:
                    print(f"KKT app is already running")
                    # App is already running
                    return True
            except Exception as e:
                print(f"Error in simplified approach, falling back to original method: {e}")
                # Fall back to original method
                check_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell "dumpsys window windows | grep -E \'mCurrentFocus\' | grep -q com.KepithorStudios.KKTFaucet && echo running || echo not_running"'
                result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
                output = result.stdout.strip()

                print(f"Original check method result: '{output}'")

                # If app is not running, start it
                if "not_running" in output or not output:
                    # Start the KKT app
                    start_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell monkey -p com.KepithorStudios.KKTFaucet -c android.intent.category.LAUNCHER 1'
                    subprocess.run(start_cmd, shell=True)
                    time.sleep(2)  # Wait for app to start
                    return True
                else:
                    # App is already running
                    return True

        except Exception as e:
            print(f"Error in open_kkt_app: {e}")
            raise Exception(f"خطا در باز کردن برنامه KKT: {e}")

    def stop_robotic_scan(self):
        """Stop robotic scanning (like automatic scan)"""
        if hasattr(self, 'robotic_scan_active') and self.robotic_scan_active:
            # Cancel the scheduled job
            if hasattr(self, 'robotic_scan_job') and self.robotic_scan_job:
                self.after_cancel(self.robotic_scan_job)
                self.robotic_scan_job = None

            # Set robotic scan as inactive
            self.robotic_scan_active = False

            # Update button text and style
            self.btn_robotic_scan.config(text="🤖 اسکن رباتیک", command=self.setup_robotic_scan, style="TButton")

            # Update status
            self.scan_status_label.config(text="وضعیت: آماده")

            # Clear countdown
            self.countdown_label.config(text="")
            if self.countdown_job:
                self.after_cancel(self.countdown_job)
                self.countdown_job = None

            # Hide progress bar
            self.scan_progress_bar.grid_remove()

            # Show a message
            messagebox.showinfo("اسکن رباتیک", "اسکن رباتیک متوقف شد.")

    def show_log_context_menu(self, event, menu):
        """Show context menu for log text widget"""
        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()

    def copy_selected_text(self, text_widget):
        """Copy selected text to clipboard"""
        try:
            # Check if there is a selection
            if text_widget.tag_ranges(tk.SEL):
                selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
                self.clipboard_clear()
                self.clipboard_append(selected_text)
                # Flash the selection to provide visual feedback
                text_widget.tag_config(tk.SEL, background='#c0c0c0')
                self.after(100, lambda: text_widget.tag_config(tk.SEL, background='#0078d7'))
                return "break"  # Prevent default behavior
            else:
                # If no selection, show a message
                print("No text selected")
        except Exception as e:
            print(f"Error copying text: {e}")
        return "break"  # Prevent default behavior

    def select_all_text(self, text_widget):
        """Select all text in the text widget"""
        try:
            # Select all text
            text_widget.tag_add(tk.SEL, "1.0", tk.END)
            # Set focus to the text widget
            text_widget.focus_set()
            return "break"  # Prevent default behavior
        except Exception as e:
            print(f"Error selecting all text: {e}")
        return "break"  # Prevent default behavior

    def get_port_number_from_display(self, port_display):
        """Get actual port number from display value (which could be title or number)"""
        try:
            # First try to find by title
            self.cursor.execute("SELECT port_number FROM ports WHERE title = ?", (port_display,))
            result = self.cursor.fetchone()

            if result:
                return result[0]

            # If not found by title, try to find by port number
            try:
                # Convert to integer if it's a number
                port_number = int(port_display)
                self.cursor.execute("SELECT port_number FROM ports WHERE port_number = ?", (port_number,))
                result = self.cursor.fetchone()
                if result:
                    return result[0]
            except ValueError:
                # Not a number, so it can't be a port number
                pass

            # Try one more approach - check if it's a MuMu port number (e.g., "45" for "MuMu 45")
            self.cursor.execute("SELECT port_number FROM ports WHERE title LIKE ?", (f"MuMu {port_display}",))
            result = self.cursor.fetchone()
            if result:
                return result[0]

            # If we get here, we couldn't find the port
            return None

        except Exception as e:
            print(f"Error in get_port_number_from_display: {e}")
            return None

    def treeview_sort_column(self, tree, col, reverse):
        """Sort treeview content when a column header is clicked"""
        # Get all items in the treeview
        data = [(tree.set(item, col), item) for item in tree.get_children('')]

        # Try to convert to numeric for proper sorting if possible
        try:
            # For score and dollar values, remove commas and convert to float
            if 'امتیاز' in tree.heading(col)['text'] or 'دلار' in tree.heading(col)['text']:
                data = [(float(val.replace(',', '').replace('$', '')) if val else 0, item) for val, item in data]
            # For date/time values, keep as string for lexicographical sorting
            elif 'تاریخ' in tree.heading(col)['text'] or 'زمان' in tree.heading(col)['text']:
                pass
            # For numeric values, convert to int if possible
            else:
                data = [(int(val) if val.isdigit() else val, item) for val, item in data]
        except (ValueError, AttributeError):
            # If conversion fails, use string comparison
            pass

        # Sort the data
        data.sort(reverse=reverse)

        # Rearrange items in sorted positions
        for idx, (val, item) in enumerate(data):
            tree.move(item, '', idx)

        # Switch the heading to the opposite sort order for next click
        tree.heading(col, command=lambda: self.treeview_sort_column(tree, col, not reverse))

    def on_closing(self):
        """Handle application closing"""
        # Cancel auto scan if active
        if self.auto_scan_active:
            self.cancel_auto_scan()

        # Cancel robotic scan if active
        if hasattr(self, 'robotic_scan_active') and self.robotic_scan_active:
            if hasattr(self, 'robotic_scan_window') and self.robotic_scan_window:
                self.stop_robotic_scan(self.robotic_scan_window)

        # Close database connection
        if hasattr(self, 'conn') and self.conn:
            self.conn.close()

        # Destroy the application
        self.destroy()



    def start_manual_scan(self):
        """Start or stop manual scan of default ports"""
        # Check if scan is already running
        if hasattr(self, 'scan_running') and self.scan_running:
            # Stop the scan
            self.stop_manual_scan()
            return

        # Get default group
        default_group = self.get_default_group()

        # Get all ports in the default group
        self.cursor.execute("""
            SELECT id, port_number FROM ports
            WHERE group_number = ?
            ORDER BY id
        """, (default_group,))

        ports = self.cursor.fetchall()

        if not ports:
            messagebox.showinfo("اطلاعات", f"هیچ پورتی در گروه {default_group} یافت نشد.")
            return

        # Update button to show stop option
        self.btn_manual_scan.config(text="⏹️ توقف اسکن", style="ManualActive.TButton")

        # Initialize scan control variables
        self.scan_running = True
        self.scan_cancelled = False
        self.scan_start_time = time.time()

        # Start scan in a separate thread
        threading.Thread(target=self.scan_ports, args=(ports,), daemon=True).start()

    def stop_manual_scan(self):
        """Stop the running manual scan"""
        if hasattr(self, 'scan_running') and self.scan_running:
            self.scan_cancelled = True
            self.scan_running = False

            # Reset button
            self.btn_manual_scan.config(text="🔍 اسکن دستی", style="TButton")

            # Update status
            self.scan_status_label.config(text="وضعیت: اسکن لغو شد")

            # Hide progress bar
            self.scan_progress_bar.grid_remove()

            # Reset progress
            self.scan_progress_var.set(0)

            messagebox.showinfo("اطلاعات", "اسکن دستی لغو شد.")

    def scan_ports(self, ports):
        """Scan ports and extract data sequentially (one by one)"""
        try:
            # Show progress bar
            self.scan_progress_bar.grid()
            self.scan_status_label.config(text="وضعیت: در حال اسکن...")

            total_ports = len(ports)
            completed_ports = 0

            # Create a new database connection
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()

            # Process each port sequentially
            for port_id, port_number in ports:
                # Check if scan was cancelled
                if hasattr(self, 'scan_cancelled') and self.scan_cancelled:
                    print("اسکن لغو شد توسط کاربر")
                    break

                # Update status
                self.scan_status_label.config(text=f"وضعیت: اسکن پورت {port_number} ({completed_ports+1} از {total_ports})")
                self.update_idletasks()

                try:
                    # Check connection
                    connection_status = self.check_port_connection_for_scan(port_number)

                    if connection_status == "متصل":
                        # Check again if scan was cancelled before taking screenshot
                        if hasattr(self, 'scan_cancelled') and self.scan_cancelled:
                            print("اسکن لغو شد قبل از گرفتن اسکرین‌شات")
                            break

                        # Take screenshot
                        screenshot_path = self.take_screenshot(port_number)

                        if screenshot_path:
                            # Check again if scan was cancelled before processing
                            if hasattr(self, 'scan_cancelled') and self.scan_cancelled:
                                print("اسکن لغو شد قبل از پردازش تصویر")
                                break

                            # Extract data
                            score, ad_count = self.extract_data_from_image(screenshot_path)

                            # Update database
                            self.update_port_data_thread_safe(cursor, port_id, score, ad_count)
                            conn.commit()  # Commit after each update
                    else:
                        # Port not connected - no update needed
                        pass

                except Exception as e:
                    print(f"Error scanning port {port_number}: {e}")

                # Update progress
                completed_ports += 1
                progress = (completed_ports / total_ports) * 100
                self.scan_progress_var.set(progress)

                # Force UI update
                self.update_idletasks()

                # Small delay to allow UI updates and cancellation checks
                time.sleep(0.1)

            # Close connection
            conn.close()

            # Make sure we're in the main thread for UI updates
            def update_ui():
                # Check if scan was cancelled
                if hasattr(self, 'scan_cancelled') and self.scan_cancelled:
                    # Scan was cancelled - don't update data, just reset UI
                    self.scan_progress_var.set(0)
                    self.scan_status_label.config(text="وضعیت: اسکن لغو شد")
                    self.btn_manual_scan.config(text="🔍 اسکن دستی", style="TButton")
                    self.after(3000, self.scan_progress_bar.grid_remove)
                    self.after(3000, lambda: self.scan_status_label.config(text="وضعیت: آماده"))
                    return

                # Calculate scan time
                if hasattr(self, 'scan_start_time'):
                    elapsed_time = time.time() - self.scan_start_time
                    self.update_scan_time_display(elapsed_time)

                # Clear the treeview first
                for item in self.scan_tree.get_children():
                    self.scan_tree.delete(item)

                # Update the treeview
                self.load_default_ports_to_scan_tree()

                # Update summary labels
                self.update_scan_summary_labels()

                # Update daily records
                self.update_daily_records()

                # Update progress to 100%
                self.scan_progress_var.set(100)
                self.scan_status_label.config(text="وضعیت: اسکن با موفقیت انجام شد")

                # Reset button
                self.btn_manual_scan.config(text="🔍 اسکن دستی", style="TButton")

                # Hide progress bar after a delay
                self.after(2000, self.scan_progress_bar.grid_remove)
                self.after(2000, lambda: self.scan_status_label.config(text="وضعیت: آماده"))

            # Schedule UI update in the main thread
            self.after(0, update_ui)

        except Exception as e:
            self.after(0, lambda: messagebox.showerror("خطا", f"خطا در اسکن پورت‌ها: {e}"))
        finally:
            self.scan_running = False

    def check_port_connection_for_scan(self, port_number):
        """Check if port is connected for scanning"""
        try:
            # Try to run adb version to check if adb is available
            try:
                subprocess.run(["adb", "version"], check=True, capture_output=True, text=True)
            except (subprocess.SubprocessError, FileNotFoundError):
                return "خطا: adb.exe یافت نشد"

            # Check if the device is already connected
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True)

            # Check if the port is in the list of connected devices
            if f"127.0.0.1:{port_number}" in result.stdout:
                return "متصل"
            else:
                # Try to connect to the port
                try:
                    connect_result = subprocess.run(
                        ["adb", "connect", f"127.0.0.1:{port_number}"],
                        capture_output=True,
                        text=True,
                        timeout=5  # Set a timeout of 5 seconds
                    )

                    # Check the result
                    if "connected" in connect_result.stdout.lower():
                        return "متصل"
                    else:
                        return "غیرمتصل"
                except subprocess.TimeoutExpired:
                    return "زمان اتصال به پایان رسید"
                except Exception as e:
                    return f"خطا: {str(e)}"
        except Exception as e:
            return f"خطا: {str(e)}"

    def take_screenshot(self, port_number):
        """Take screenshot of the device"""
        try:
            # Create device ID
            device_id = f"127.0.0.1:{port_number}"

            # Create screenshot path
            screenshot_path = os.path.join(SCREENSHOT_DIR, f"port_{port_number}.png")

            # Take screenshot using adb
            subprocess.run(
                ["adb", "-s", device_id, "shell", "screencap", "-p", "/sdcard/screen.png"],
                check=True,
                capture_output=True
            )

            # Pull screenshot from device
            subprocess.run(
                ["adb", "-s", device_id, "pull", "/sdcard/screen.png", screenshot_path],
                check=True,
                capture_output=True
            )

            # Check if screenshot was taken
            if os.path.exists(screenshot_path):
                return screenshot_path
            else:
                return None
        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return None

    def extract_data_from_image(self, image_path):
        """Extract score and ad count from image using improved methods from exportmumu.py"""
        try:
            # Open image
            image = Image.open(image_path)
            width, height = image.size
            port_number = os.path.basename(image_path).replace("port_", "").replace(".png", "")

            # ================ Process score region ================
            # Crop score region with original coordinates
            crop_score = image.crop((int(width*0.60), int(height*0.03), int(width*0.95), int(height*0.12)))

            # Simple and effective processing like exportmumu.py
            score_gray = crop_score.convert("L")
            score_binary = score_gray.point(lambda p: 255 if p > 150 else 0)

            # Save processed images for debugging
            score_debug_path = os.path.join(SCREENSHOT_DIR, f"score_debug_port_{port_number}.png")
            score_binary.save(score_debug_path)

            # ================ Process ad count region ================
            # Crop ad count region with original coordinates
            crop_ads = image.crop((int(width*0.70), int(height*0.35), int(width*0.92), int(height*0.42)))

            # Simple and effective processing like exportmumu.py
            ads_gray = crop_ads.convert("L")
            ads_binary = ads_gray.point(lambda p: 255 if p > 150 else 0)

            # Save processed image for debugging
            ads_debug_path = os.path.join(SCREENSHOT_DIR, f"ads_debug_0_port_{port_number}.png")
            ads_binary.save(ads_debug_path)

            # ================ Extract text with OCR using exact method from exportmumu.py ================
            # Extract score text - exactly like exportmumu.py
            score_text = pytesseract.image_to_string(score_binary, lang='eng')

            # Extract ad count text - exactly like exportmumu.py
            ads_text = pytesseract.image_to_string(ads_binary, config="--psm 7 digits", lang='eng')

            # Print OCR results for debugging
            print(f"Score text: '{score_text}'")
            print(f"Ads text: '{ads_text}'")

            # ================ Extract numbers from text using exact method from exportmumu.py ================
            # Extract score - exactly like exportmumu.py
            try:
                numbers = re.findall(r"\d+\.\d+", score_text)
                score = float(numbers[0]) if numbers else 0
                print(f"Found score: {score}")
            except Exception as e:
                print(f"Error extracting score: {e}")
                score = 0

            # Extract ad count - exactly like exportmumu.py
            try:
                numbers = re.findall(r"\d+", ads_text)
                ad_count = int(numbers[0]) if numbers else 0
                print(f"Found ad count: {ad_count}")
            except Exception as e:
                print(f"Error extracting ad count: {e}")
                ad_count = 0

            # Log final results
            print(f"Final extracted score: {score}")
            print(f"Final extracted ad count: {ad_count}")

            # If score is still 0, keep it as 0 but log it
            if score == 0:
                print(f"Could not extract score for port {port_number}, using 0")

            # If ad count is still 0, keep it as 0 but log it
            if ad_count == 0:
                print(f"Could not extract ad count for port {port_number}, using 0")

            # Don't overwrite existing non-zero values in the database with zeros from OCR
            # This will be handled in the update_port_data_thread_safe method

            return score, ad_count

        except Exception as e:
            print(f"Error extracting data from image {image_path}: {e}")
            return 0, 0

    def update_port_data(self, port_id, score, ad_count):
        """Update port data in database (for main thread) with improved handling of zero values"""
        try:
            # Get current date and time
            now = datetime.now()
            current_date = now.strftime("%Y/%m/%d")
            current_time = now.strftime("%H:%M:%S")

            # Get current values from database
            self.cursor.execute("SELECT score, ad_count FROM ports WHERE id = ?", (port_id,))
            result = self.cursor.fetchone()

            update_score = score
            update_ad_count = ad_count

            if result:
                current_score, current_ad_count = result

                # Handle score - don't overwrite existing non-zero values with zeros from OCR
                if score == 0 and current_score > 0:
                    # Keep the existing non-zero score
                    update_score = current_score
                    print(f"Keeping existing score {current_score} for port {port_id} (OCR returned 0)")
                elif score > 0:
                    # Use the new non-zero score from OCR
                    update_score = score
                    print(f"Updating score to {score} for port {port_id}")
                else:
                    # Both are zero, keep it as zero
                    update_score = 0
                    print(f"Both current and new score are 0 for port {port_id}")

                # Handle ad count - don't overwrite existing non-zero values with zeros from OCR
                if ad_count == 0 and current_ad_count > 0:
                    # Keep the existing non-zero ad count
                    update_ad_count = current_ad_count
                    print(f"Keeping existing ad count {current_ad_count} for port {port_id} (OCR returned 0)")
                elif ad_count > 0:
                    # Use the new non-zero ad count from OCR
                    update_ad_count = ad_count
                    print(f"Updating ad count to {ad_count} for port {port_id}")
                else:
                    # Both are zero, keep it as zero
                    update_ad_count = 0
                    print(f"Both current and new ad count are 0 for port {port_id}")
            else:
                # No existing record, use the new values
                update_score = score
                update_ad_count = ad_count
                print(f"No existing record for port {port_id}, using new values: score={score}, ad_count={ad_count}")

            # Save current values as previous values before updating
            self.cursor.execute(
                """UPDATE ports SET
                   previous_score = score,
                   previous_ad_count = ad_count,
                   score = ?,
                   ad_count = ?,
                   update_date = ?,
                   update_time = ?
                   WHERE id = ?""",
                (update_score, update_ad_count, current_date, current_time, port_id)
            )
            self.conn.commit()

        except Exception as e:
            print(f"Error updating port data: {e}")

    def update_port_data_thread_safe(self, cursor, port_id, score, ad_count):
        """Update port data in database (thread-safe version) with improved handling of zero values"""
        try:
            # Get current date and time
            now = datetime.now()
            current_date = now.strftime("%Y/%m/%d")
            current_time = now.strftime("%H:%M:%S")

            # Get current values from database
            cursor.execute("SELECT score, ad_count FROM ports WHERE id = ?", (port_id,))
            result = cursor.fetchone()

            update_score = score
            update_ad_count = ad_count

            if result:
                current_score, current_ad_count = result

                # Handle score - don't overwrite existing non-zero values with zeros from OCR
                if score == 0 and current_score > 0:
                    # Keep the existing non-zero score
                    update_score = current_score
                    print(f"Keeping existing score {current_score} for port {port_id} (OCR returned 0)")
                elif score > 0:
                    # Use the new non-zero score from OCR
                    update_score = score
                    print(f"Updating score to {score} for port {port_id}")
                else:
                    # Both are zero, keep it as zero
                    update_score = 0
                    print(f"Both current and new score are 0 for port {port_id}")

                # Handle ad count - don't overwrite existing non-zero values with zeros from OCR
                if ad_count == 0 and current_ad_count > 0:
                    # Keep the existing non-zero ad count
                    update_ad_count = current_ad_count
                    print(f"Keeping existing ad count {current_ad_count} for port {port_id} (OCR returned 0)")
                elif ad_count > 0:
                    # Use the new non-zero ad count from OCR
                    update_ad_count = ad_count
                    print(f"Updating ad count to {ad_count} for port {port_id}")
                else:
                    # Both are zero, keep it as zero
                    update_ad_count = 0
                    print(f"Both current and new ad count are 0 for port {port_id}")
            else:
                # No existing record, use the new values
                update_score = score
                update_ad_count = ad_count
                print(f"No existing record for port {port_id}, using new values: score={score}, ad_count={ad_count}")

            # Save current values as previous values before updating
            cursor.execute(
                """UPDATE ports SET
                   previous_score = score,
                   previous_ad_count = ad_count,
                   score = ?,
                   ad_count = ?,
                   update_date = ?,
                   update_time = ?
                   WHERE id = ?""",
                (update_score, update_ad_count, current_date, current_time, port_id)
            )

        except Exception as e:
            print(f"Error updating port data (thread-safe): {e}")



    def show_score_screenshot(self):
        """Show score screenshot for selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        port_display = item['values'][1]

        # Find the actual port number
        port_number = self.get_port_number_from_display(port_display)
        if not port_number:
            messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
            return

        # Take fresh screenshot and process it
        try:
            import tempfile
            from PIL import Image, ImageEnhance

            # Create temporary file for screenshot
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            screenshot_path = temp_file.name
            temp_file.close()

            # Create temporary file for processed score image
            temp_score_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            score_debug_path = temp_score_file.name
            temp_score_file.close()

            # Take fresh screenshot
            if self.take_screenshot_to_path(port_number, screenshot_path):
                # Open image and process score region
                image = Image.open(screenshot_path)
                width, height = image.size

                # Crop score region with original coordinates
                crop_score = image.crop((int(width*0.60), int(height*0.03), int(width*0.95), int(height*0.12)))

                # Convert to black and white with better contrast
                enhancer = ImageEnhance.Contrast(crop_score)
                score_enhanced = enhancer.enhance(2.0)
                score_bw = score_enhanced.convert("L").point(lambda p: 255 if p > 145 else 0)

                # Save processed image
                score_bw.save(score_debug_path)

                # Show the image
                self.show_image(score_debug_path, f"اسکن امتیاز - پورت {port_number}")

                # Clean up temp files
                try:
                    import os
                    os.unlink(screenshot_path)
                    os.unlink(score_debug_path)
                except:
                    pass
            else:
                messagebox.showerror("خطا", f"خطا در گرفتن اسکرین‌شات از پورت {port_number}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش تصویر: {e}")

    def show_ads_screenshot(self):
        """Show ads screenshot for selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        port_display = item['values'][1]

        # Find the actual port number
        port_number = self.get_port_number_from_display(port_display)
        if not port_number:
            messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
            return

        # Take fresh screenshot and process it
        try:
            import tempfile
            from PIL import Image, ImageEnhance

            # Create temporary file for screenshot
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            screenshot_path = temp_file.name
            temp_file.close()

            # Create temporary file for processed ads image
            temp_ads_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            ads_debug_path = temp_ads_file.name
            temp_ads_file.close()

            # Take fresh screenshot
            if self.take_screenshot_to_path(port_number, screenshot_path):
                # Open image and process ads region
                image = Image.open(screenshot_path)
                width, height = image.size

                # Crop ads region with original coordinates
                crop_ads = image.crop((int(width*0.70), int(height*0.35), int(width*0.92), int(height*0.42)))

                # Convert to black and white with better contrast
                enhancer = ImageEnhance.Contrast(crop_ads)
                ads_enhanced = enhancer.enhance(2.0)
                ads_bw = ads_enhanced.convert("L").point(lambda p: 255 if p > 145 else 0)

                # Increase sharpness
                enhancer = ImageEnhance.Sharpness(ads_bw)
                ads_sharp = enhancer.enhance(1.5)

                # Save processed image
                ads_sharp.save(ads_debug_path)

                # Show the image
                self.show_image(ads_debug_path, f"اسکن تبلیغات - پورت {port_number}")

                # Clean up temp files
                try:
                    import os
                    os.unlink(screenshot_path)
                    os.unlink(ads_debug_path)
                except:
                    pass
            else:
                messagebox.showerror("خطا", f"خطا در گرفتن اسکرین‌شات از پورت {port_number}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در پردازش تصویر: {e}")

    def show_image(self, image_path, title):
        """Show image in a new window"""
        try:
            # Create a new window
            image_window = tk.Toplevel(self)
            image_window.title(title)
            image_window.transient(self)
            image_window.grab_set()

            # Open and resize image if needed
            image = Image.open(image_path)

            # Create a PhotoImage object
            from PIL import ImageTk
            photo = ImageTk.PhotoImage(image)

            # Create a label to display the image
            label = ttk.Label(image_window, image=photo)
            label.image = photo  # Keep a reference to avoid garbage collection
            label.pack(padx=10, pady=10)

            # Add a close button
            close_button = ttk.Button(image_window, text="بستن", command=image_window.destroy)
            close_button.pack(pady=10)

            # Center the window
            image_window.update_idletasks()
            width = image_window.winfo_width()
            height = image_window.winfo_height()
            x = (image_window.winfo_screenwidth() // 2) - (width // 2)
            y = (image_window.winfo_screenheight() // 2) - (height // 2)
            image_window.geometry(f"{width}x{height}+{x}+{y}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در نمایش تصویر: {e}")

    def rescan_selected_port(self):
        """Rescan selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port number and ID
        item = self.scan_tree.item(selected_items[0])
        port_id = None
        port_display = item['values'][1]  # This could be port number or title

        # Try to find port by title first
        try:
            # First try to find by title
            self.cursor.execute("SELECT id, port_number FROM ports WHERE title = ?", (port_display,))
            result = self.cursor.fetchone()

            if not result:
                # If not found by title, try to find by port number
                try:
                    # Convert to integer if it's a number
                    port_number = int(port_display)
                    self.cursor.execute("SELECT id, port_number FROM ports WHERE port_number = ?", (port_number,))
                    result = self.cursor.fetchone()
                except ValueError:
                    # Not a number, so it can't be a port number
                    result = None

            if result:
                port_id = result[0]
                port_number = result[1]  # Get the actual port number
            else:
                # Try one more approach - check if it's a MuMu port number (e.g., "45" for "MuMu 45")
                try:
                    # Check if it's a MuMu port with just the number
                    self.cursor.execute("SELECT id, port_number FROM ports WHERE title LIKE ?", (f"MuMu {port_display}",))
                    result = self.cursor.fetchone()
                    if result:
                        port_id = result[0]
                        port_number = result[1]
                    else:
                        messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
                        return
                except Exception:
                    messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
                    return
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی اطلاعات پورت: {e}")
            return

        # Check if scan is already running
        if hasattr(self, 'scan_running') and self.scan_running:
            messagebox.showinfo("اطلاعات", "اسکن در حال اجراست. لطفاً صبر کنید.")
            return

        # Start scan in a separate thread
        self.scan_running = True
        self.scan_start_time = time.time()
        threading.Thread(target=self.scan_single_port, args=(port_id, port_number), daemon=True).start()

    def scan_single_port(self, port_id, port_number):
        """Scan a single port and extract data"""
        try:
            # Show progress bar
            self.scan_progress_bar.grid()
            self.scan_status_label.config(text=f"وضعیت: در حال اسکن پورت {port_number}...")
            self.scan_progress_var.set(10)
            self.update_idletasks()

            # Create a new database connection for this thread
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()

            # Check connection
            self.scan_progress_var.set(20)
            self.scan_status_label.config(text=f"وضعیت: بررسی اتصال پورت {port_number}...")
            self.update_idletasks()

            connection_status = self.check_port_connection_for_scan(port_number)

            if connection_status == "متصل":
                # Take screenshot
                self.scan_progress_var.set(40)
                self.scan_status_label.config(text=f"وضعیت: گرفتن اسکرین‌شات از پورت {port_number}...")
                self.update_idletasks()

                screenshot_path = self.take_screenshot(port_number)

                if screenshot_path:
                    # Extract data
                    self.scan_progress_var.set(60)
                    self.scan_status_label.config(text=f"وضعیت: استخراج داده‌ها از اسکرین‌شات پورت {port_number}...")
                    self.update_idletasks()

                    score, ad_count = self.extract_data_from_image(screenshot_path)

                    # Update database
                    self.scan_progress_var.set(80)
                    self.scan_status_label.config(text=f"وضعیت: به‌روزرسانی اطلاعات پورت {port_number}...")
                    self.update_idletasks()

                    self.update_port_data_thread_safe(cursor, port_id, score, ad_count)
                    conn.commit()

                    # No success message needed
                else:
                    # Failed to take screenshot - no message needed
                    pass
            else:
                # Port not connected - no message needed
                pass

            # Close connection
            conn.close()

            # Make sure we're in the main thread for UI updates
            def update_ui():
                # Calculate scan time
                if hasattr(self, 'scan_start_time'):
                    elapsed_time = time.time() - self.scan_start_time
                    self.update_scan_time_display(elapsed_time)

                # Clear the treeview first
                for item in self.scan_tree.get_children():
                    self.scan_tree.delete(item)

                # Update the treeview
                self.load_default_ports_to_scan_tree()

                # Update summary labels
                self.update_scan_summary_labels()

                # Update daily records
                self.update_daily_records()

                # Update progress to 100%
                self.scan_progress_var.set(100)
                self.scan_status_label.config(text="وضعیت: اسکن با موفقیت انجام شد")

                # Hide progress bar after a delay
                self.after(2000, self.scan_progress_bar.grid_remove)
                self.after(2000, lambda: self.scan_status_label.config(text="وضعیت: آماده"))

            # Schedule UI update in the main thread
            self.after(0, update_ui)

        except Exception as e:
            self.after(0, lambda: messagebox.showerror("خطا", f"خطا در اسکن پورت: {e}"))
        finally:
            self.scan_running = False

    def show_selected_port(self):
        """Show live screenshot for selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        port_display = item['values'][1]

        # Find the actual port number
        port_number = self.get_port_number_from_display(port_display)
        if not port_number:
            messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
            return

        # Take a fresh screenshot (without saving)
        try:
            import tempfile
            import os

            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # Take screenshot to temp file
            if self.take_screenshot_to_path(port_number, temp_path):
                # Show the image
                self.show_image(temp_path, f"نمایش زنده - پورت {port_number}")

                # Clean up temp file after showing
                try:
                    os.unlink(temp_path)
                except:
                    pass
            else:
                messagebox.showerror("خطا", f"خطا در گرفتن اسکرین‌شات از پورت {port_number}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در نمایش اسکرین: {e}")

    def take_screenshot_to_path(self, port_number, output_path):
        """Take screenshot and save to specific path"""
        try:
            import subprocess
            import os

            # Get ADB path
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists
            if not os.path.exists(adb_path):
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    adb_path = "adb"
                except (subprocess.SubprocessError, FileNotFoundError):
                    print("adb.exe یافت نشد")
                    return False

            # Take screenshot
            screencap_cmd = f'{adb_path} -s 127.0.0.1:{port_number} exec-out screencap -p'
            screencap_result = subprocess.run(screencap_cmd, shell=True, capture_output=True)

            if screencap_result.returncode == 0:
                # Save screenshot
                with open(output_path, 'wb') as f:
                    f.write(screencap_result.stdout)
                return True
            else:
                print(f"خطا در گرفتن اسکرین‌شات: {screencap_result.stderr}")
                return False

        except Exception as e:
            print(f"خطا در take_screenshot_to_path: {e}")
            return False

    def detect_and_select_server(self):
        """Detect and select server for selected port"""
        selected_items = self.scan_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port display (could be number or title)
        item = self.scan_tree.item(selected_items[0])
        port_display = item['values'][1]

        # Find the actual port number and port ID
        port_number = self.get_port_number_from_display(port_display)
        if not port_number:
            messagebox.showwarning("هشدار", f"پورت {port_display} در دیتابیس یافت نشد.")
            return

        # Get port ID
        try:
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM ports WHERE port_number = ?", (port_number,))
            result = cursor.fetchone()
            conn.close()

            if not result:
                messagebox.showwarning("هشدار", f"پورت {port_number} در دیتابیس یافت نشد.")
                return

            port_id = result[0]

            # Show a progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("تشخیص و انتخاب سرور")
            progress_window.geometry("400x150")
            progress_window.transient(self)
            progress_window.grab_set()

            # Add a label
            status_label = ttk.Label(progress_window, text=f"در حال گرفتن اسکرین‌شات از پورت {port_number}...")
            status_label.pack(pady=10)

            # Add a progress bar
            progress_var = tk.IntVar()
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100)
            progress_bar.pack(fill='x', padx=10, pady=10)

            # Update progress
            progress_var.set(10)
            progress_window.update()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی اطلاعات پورت: {e}")
            return

        try:
            # Update progress
            progress_var.set(30)
            status_label.config(text=f"در حال گرفتن اسکرین‌شات...")
            progress_window.update()

            # Take screenshot
            screenshot_path = self.take_port_screenshot(port_id, port_number)

            # Update progress
            progress_var.set(60)
            status_label.config(text=f"در حال تشخیص سرور...")
            progress_window.update()

            # Try to detect and click server
            result = self.detect_and_click_server(port_id, port_number, screenshot_path)

            # Update progress
            progress_var.set(100)
            progress_window.update()

            # Close progress window
            progress_window.destroy()

            if result:
                messagebox.showinfo("موفقیت", f"سرور مناسب در پورت {port_number} تشخیص داده شد و انتخاب شد.")
            else:
                messagebox.showinfo("اطلاعات", f"هیچ سرور مناسبی در پورت {port_number} یافت نشد یا صفحه انتخاب سرور باز نیست.")

        except Exception as e:
            # Close progress window
            try:
                progress_window.destroy()
            except:
                pass
            messagebox.showerror("خطا", f"خطا در تشخیص سرور: {e}")

    def save_default_group(self):
        """Save the selected default group to the database and set all ports in that group as default"""
        default_group = self.default_group_var.get()

        try:
            # Update the default_group setting
            self.cursor.execute(
                "UPDATE settings SET setting_value = ? WHERE setting_name = 'default_group'",
                (str(default_group),)
            )

            # Set all ports in the other group as non-default
            other_group = 2 if default_group == 1 else 1
            self.cursor.execute(
                "UPDATE ports SET is_default = 0 WHERE group_number = ?",
                (other_group,)
            )

            # Set all ports in the selected group as default
            # Only if there are ports in this group
            self.cursor.execute(
                "SELECT COUNT(*) FROM ports WHERE group_number = ?",
                (default_group,)
            )
            count = self.cursor.fetchone()[0]

            if count > 0:
                self.cursor.execute(
                    "UPDATE ports SET is_default = 1 WHERE group_number = ?",
                    (default_group,)
                )

            self.conn.commit()
            messagebox.showinfo("موفقیت", f"گروه {default_group} به عنوان گروه پیش‌فرض ذخیره شد و تمام پورت‌های آن پیش‌فرض شدند")

            # Reload ports to show changes
            self.load_ports()

            # Also update the scan treeview to show the new default ports
            self.load_default_ports_to_scan_tree()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره تنظیمات: {e}")

    def open_port_management(self):
        """Open the port management window"""
        # Create a new toplevel window
        port_window = tk.Toplevel(self)
        port_window.title("مدیریت پورت‌ها")
        port_window.geometry("800x600")  # Increased size for better layout
        port_window.transient(self)  # Set to be on top of the main window
        port_window.grab_set()  # Modal window

        # Create the port management form
        self.create_port_management_form(port_window)

    def create_port_management_form(self, parent):
        """Create the port management form elements"""
        # Main frame
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Default group selection frame
        default_group_frame = ttk.LabelFrame(main_frame, text="انتخاب گروه پیش‌فرض")
        default_group_frame.pack(fill='x', padx=10, pady=10)

        # Get current default group from database
        try:
            self.cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'default_group'")
            result = self.cursor.fetchone()
            default_group = int(result[0]) if result else 1
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی تنظیمات: {e}")
            default_group = 1

        # Create a frame for better layout
        default_group_content = ttk.Frame(default_group_frame)
        default_group_content.pack(fill='x', padx=5, pady=5)

        # Create radio buttons for default group selection
        self.default_group_var = tk.IntVar(value=default_group)

        group_label = ttk.Label(default_group_content, text="گروه پیش‌فرض:")
        group_label.pack(side='right', padx=5)

        group1_radio = ttk.Radiobutton(default_group_content, text="گروه 1",
                                      variable=self.default_group_var, value=1)
        group1_radio.pack(side='right', padx=5)

        group2_radio = ttk.Radiobutton(default_group_content, text="گروه 2",
                                      variable=self.default_group_var, value=2)
        group2_radio.pack(side='right', padx=5)

        # Save default group button
        save_default_btn = ttk.Button(default_group_content, text="ذخیره گروه پیش‌فرض",
                                     command=self.save_default_group, width=15)
        save_default_btn.pack(side='right', padx=20)

        # Input frame for port information
        input_frame = ttk.LabelFrame(main_frame, text="اطلاعات پورت")
        input_frame.pack(fill='x', padx=10, pady=10)

        # Port number input and main buttons in one row
        port_frame = ttk.Frame(input_frame)
        port_frame.pack(fill='x', padx=10, pady=10)

        port_label = ttk.Label(port_frame, text="شماره پورت (5 رقمی):")
        port_label.pack(side='right', padx=5)

        vcmd = (parent.register(self.validate_port), '%P')
        self.port_entry = ttk.Entry(port_frame, validate="key", validatecommand=vcmd, width=10)
        self.port_entry.pack(side='right', padx=5)

        # Bind Enter key to add_port function
        self.port_entry.bind('<Return>', lambda event: self.add_port())

        # Add main buttons next to the port entry
        add_btn = ttk.Button(port_frame, text="ثبت", command=self.add_port, width=8)
        add_btn.pack(side='right', padx=5)

        edit_btn = ttk.Button(port_frame, text="ویرایش", command=self.edit_port, width=8)
        edit_btn.pack(side='right', padx=5)

        delete_btn = ttk.Button(port_frame, text="حذف", command=self.delete_port, width=8)
        delete_btn.pack(side='right', padx=5)

        # Group selection
        group_frame = ttk.Frame(input_frame)
        group_frame.pack(fill='x', padx=10, pady=10)

        group_label = ttk.Label(group_frame, text="گروه:")
        group_label.pack(side='right', padx=5)

        self.group_var = tk.IntVar(value=1)

        group1_radio = ttk.Radiobutton(group_frame, text="گروه 1",
                                      variable=self.group_var, value=1)
        group1_radio.pack(side='right', padx=5)

        group2_radio = ttk.Radiobutton(group_frame, text="گروه 2",
                                      variable=self.group_var, value=2)
        group2_radio.pack(side='right', padx=5)

        # Action buttons row
        action_frame = ttk.Frame(input_frame)
        action_frame.pack(fill='x', padx=10, pady=5)

        # Left side buttons
        left_buttons_frame = ttk.Frame(action_frame)
        left_buttons_frame.pack(side='left', fill='x', expand=True)

        delete_all_btn = ttk.Button(left_buttons_frame, text="حذف همه پورت‌ها",
                                   command=self.delete_all_ports, width=20)
        delete_all_btn.pack(side='left', padx=5)

        # Right side buttons
        right_buttons_frame = ttk.Frame(action_frame)
        right_buttons_frame.pack(side='right', fill='x')

        set_default_btn = ttk.Button(right_buttons_frame, text="تغییر وضعیت پیش‌فرض",
                                    command=self.set_port_as_default, width=20)
        set_default_btn.pack(side='right', padx=5)

        # Create a frame for connection buttons with better layout
        conn_frame = ttk.LabelFrame(input_frame, text="مدیریت اتصال")
        conn_frame.pack(fill='x', padx=10, pady=10)

        # Top row - check connections
        check_row = ttk.Frame(conn_frame)
        check_row.pack(fill='x', padx=5, pady=5)

        check_btn = ttk.Button(check_row, text="بررسی اتصال پورت انتخاب شده",
                              command=self.check_port_connection, width=25)
        check_btn.pack(side='right', padx=5)

        check_all_btn = ttk.Button(check_row, text="بررسی اتصال گروه پیش‌فرض",
                                  command=self.check_default_group_connections, width=25)
        check_all_btn.pack(side='right', padx=5)

        # Bottom row - disconnect
        disconnect_row = ttk.Frame(conn_frame)
        disconnect_row.pack(fill='x', padx=5, pady=5)

        disconnect_btn = ttk.Button(disconnect_row, text="قطع اتصال پورت انتخاب شده",
                                   command=self.disconnect_port, width=25)
        disconnect_btn.pack(side='right', padx=5)

        disconnect_all_btn = ttk.Button(disconnect_row, text="قطع اتصال همه پورت‌ها",
                                      command=self.disconnect_all_ports, width=25)
        disconnect_all_btn.pack(side='right', padx=5)

        # Treeview for displaying ports
        tree_frame = ttk.LabelFrame(main_frame, text="لیست پورت‌ها")
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create Treeview
        columns = ('row', 'connection_status', 'is_default', 'group_number', 'port_number', 'title', 'id')
        self.port_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')

        # Define headings with sort functionality - right to left order for Persian
        self.port_tree.heading('row', text='ردیف',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'row', False))
        self.port_tree.heading('connection_status', text='وضعیت اتصال',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'connection_status', False))
        self.port_tree.heading('is_default', text='پیش‌فرض',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'is_default', False))
        self.port_tree.heading('group_number', text='گروه',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'group_number', False))
        self.port_tree.heading('port_number', text='شماره پورت',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'port_number', False))
        self.port_tree.heading('title', text='عنوان',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'title', False))
        self.port_tree.heading('id', text='شناسه',
                              command=lambda: self.treeview_sort_column(self.port_tree, 'id', False))

        # Define columns
        self.port_tree.column('row', width=50, anchor='center')
        self.port_tree.column('connection_status', width=100, anchor='center')
        self.port_tree.column('is_default', width=70, anchor='center')
        self.port_tree.column('group_number', width=50, anchor='center')
        self.port_tree.column('port_number', width=100, anchor='center')
        self.port_tree.column('title', width=100, anchor='center')
        self.port_tree.column('id', width=50, anchor='center')

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.port_tree.yview)
        self.port_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        self.port_tree.pack(fill='both', expand=True, padx=5, pady=5)

        # Bind select event
        self.port_tree.bind('<<TreeviewSelect>>', self.on_tree_select)

        # Load existing ports
        self.load_ports()

    def validate_port(self, value):
        """Validate port number input - must be 5 digits"""
        if value == "":
            return True
        if value.isdigit() and len(value) <= 5:
            return True
        return False

    def load_ports(self):
        """Load ports from database into treeview"""
        # Clear existing items
        for item in self.port_tree.get_children():
            self.port_tree.delete(item)

        # Get ports from database
        try:
            # Try to get all columns
            try:
                self.cursor.execute("""
                    SELECT id, port_number, group_number, is_default, connection_status,
                           score, ad_count, update_date, update_time, title
                    FROM ports
                    ORDER BY id
                """)
                ports = self.cursor.fetchall()

                # Add to treeview
                row_counter = 1
                for port in ports:
                    # Convert is_default to بله/خیر for display
                    port_values = list(port)
                    port_values[3] = "بله" if port[3] == 1 else "خیر"

                    # If connection_status is None or empty, set it to "نامشخص"
                    if not port_values[4]:
                        port_values[4] = "نامشخص"

                    # If title is None or empty, generate a default title
                    if not port_values[9]:
                        if port_values[2] == 1:  # Group 1
                            title_num = min(port_values[0], 40)  # Max 40 for group 1
                            port_values[9] = f"MuMu {title_num}"
                        else:  # Group 2
                            title_num = min(40 + port_values[0], 80)  # Start from 41, max 80
                            port_values[9] = f"MuMu {title_num}"

                    # Create a list with values in the correct order for display
                    # Order: row, connection_status, is_default, group_number, port_number, title, id
                    display_values = [
                        row_counter,                # ردیف
                        port_values[4],             # وضعیت اتصال
                        port_values[3],             # پیش‌فرض
                        port_values[2],             # گروه
                        port_values[1],             # شماره پورت
                        port_values[9],             # عنوان
                        port_values[0]              # شناسه
                    ]

                    self.port_tree.insert('', 'end', values=display_values)
                    row_counter += 1
            except sqlite3.OperationalError:
                # Fallback to basic columns if new columns don't exist yet
                try:
                    self.cursor.execute("SELECT id, port_number, group_number, is_default, connection_status FROM ports ORDER BY id")
                    ports = self.cursor.fetchall()

                    # Add to treeview
                    row_counter = 1
                    for port in ports:
                        # Convert is_default to بله/خیر for display
                        port_values = list(port)
                        port_values[3] = "بله" if port[3] == 1 else "خیر"

                        # If connection_status is None or empty, set it to "نامشخص"
                        if not port_values[4]:
                            port_values[4] = "نامشخص"

                        # Add default values for missing columns
                        port_values.extend([0, 0, "", ""])

                        # Create a new list with row number as the first element
                        display_values = [row_counter] + port_values

                        self.port_tree.insert('', 'end', values=display_values)
                        row_counter += 1
                except sqlite3.OperationalError:
                    # If connection_status column doesn't exist, use a simpler query
                    self.cursor.execute("SELECT id, port_number, group_number, is_default FROM ports ORDER BY id")
                    ports = self.cursor.fetchall()

                    # Add to treeview
                    row_counter = 1
                    for port in ports:
                        # Convert is_default to بله/خیر for display
                        port_values = list(port)
                        port_values[3] = "بله" if port[3] == 1 else "خیر"

                        # Add default values for missing columns
                        port_values.extend(["نامشخص", 0, 0, "", ""])

                        # Create a list with values in the correct order for display
                        # Order: row, connection_status, is_default, group_number, port_number, id
                        display_values = [
                            row_counter,                # ردیف
                            port_values[4],             # وضعیت اتصال
                            port_values[3],             # پیش‌فرض
                            port_values[2],             # گروه
                            port_values[1],             # شماره پورت
                            port_values[0]              # شناسه
                        ]

                        self.port_tree.insert('', 'end', values=display_values)
                        row_counter += 1

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری پورت‌ها: {e}")

    def add_port(self):
        """Add a new port to the database"""
        port_number = self.port_entry.get().strip()
        group_number = self.group_var.get()

        # Validate input
        if not port_number:
            messagebox.showerror("خطا", "لطفا شماره پورت را وارد کنید")
            return

        if not port_number.isdigit() or len(port_number) != 5:
            messagebox.showerror("خطا", "شماره پورت باید 5 رقمی باشد")
            return

        # Add to database
        try:
            # Get current date and time
            now = datetime.now()
            current_date = now.strftime("%Y/%m/%d")
            current_time = now.strftime("%H:%M:%S")

            # Get the next title number based on group
            # For group 1: MuMu 1 to MuMu 40
            # For group 2: MuMu 41 to MuMu 80
            if group_number == 1:
                # Get the highest title number in group 1
                self.cursor.execute("""
                    SELECT title FROM ports
                    WHERE group_number = 1 AND title LIKE 'MuMu %'
                    ORDER BY CAST(SUBSTR(title, 6) AS INTEGER) DESC
                    LIMIT 1
                """)
                result = self.cursor.fetchone()

                if result and result[0]:
                    # Extract number from title (e.g., "MuMu 5" -> 5)
                    try:
                        last_num = int(result[0].split()[1])
                        next_num = last_num + 1
                    except (ValueError, IndexError):
                        next_num = 1
                else:
                    next_num = 1

                # Ensure number is within range 1-40
                if next_num > 40:
                    messagebox.showwarning("هشدار", "تعداد پورت‌های گروه 1 به حداکثر رسیده است (40)")
                    next_num = 40

                title = f"MuMu {next_num}"

            else:  # group_number == 2
                # Get the highest title number in group 2
                self.cursor.execute("""
                    SELECT title FROM ports
                    WHERE group_number = 2 AND title LIKE 'MuMu %'
                    ORDER BY CAST(SUBSTR(title, 6) AS INTEGER) DESC
                    LIMIT 1
                """)
                result = self.cursor.fetchone()

                if result and result[0]:
                    # Extract number from title (e.g., "MuMu 45" -> 45)
                    try:
                        last_num = int(result[0].split()[1])
                        next_num = last_num + 1
                    except (ValueError, IndexError):
                        next_num = 41
                else:
                    next_num = 41

                # Ensure number is within range 41-80
                if next_num > 80:
                    messagebox.showwarning("هشدار", "تعداد پورت‌های گروه 2 به حداکثر رسیده است (40)")
                    next_num = 80
                elif next_num < 41:
                    next_num = 41

                title = f"MuMu {next_num}"

            self.cursor.execute(
                """INSERT INTO ports
                   (port_number, group_number, is_default, connection_status, score, ad_count, update_date, update_time, title)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (int(port_number), group_number, 0, "نامشخص", 0, 0, current_date, current_time, title)
            )
            self.conn.commit()
            messagebox.showinfo("موفقیت", f"پورت با موفقیت ثبت شد (عنوان: {title})")

            # Clear entry and reload ports
            self.port_entry.delete(0, tk.END)
            self.load_ports()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "این شماره پورت قبلا ثبت شده است")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت پورت: {e}")

    def on_tree_select(self, event):
        """Handle treeview selection"""
        selected_items = self.port_tree.selection()
        if selected_items:
            # Get values of selected item
            item = self.port_tree.item(selected_items[0])
            values = item['values']

            # Set values in entry fields
            self.port_entry.delete(0, tk.END)
            self.port_entry.insert(0, str(values[4]))  # Port number is now at index 4
            self.group_var.set(values[3])  # Group number is now at index 3

            # Display title in status bar or somewhere else if needed
            title = values[5]  # Title is now at index 5

    def edit_port(self):
        """Edit selected port"""
        selected_items = self.port_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected item ID
        item = self.port_tree.item(selected_items[0])
        port_id = item['values'][6]  # ID is now at index 6

        # Get new values
        port_number = self.port_entry.get().strip()
        group_number = self.group_var.get()

        # Validate input
        if not port_number:
            messagebox.showerror("خطا", "لطفا شماره پورت را وارد کنید")
            return

        if not port_number.isdigit() or len(port_number) != 5:
            messagebox.showerror("خطا", "شماره پورت باید 5 رقمی باشد")
            return

        # Update database
        try:
            # Get current port data
            self.cursor.execute("SELECT connection_status, score, ad_count, title FROM ports WHERE id = ?", (port_id,))
            result = self.cursor.fetchone()

            if result:
                connection_status = result[0] if result[0] else "نامشخص"
                score = result[1] if result[1] is not None else 0
                ad_count = result[2] if result[2] is not None else 0
                title = result[3]
            else:
                connection_status = "نامشخص"
                score = 0
                ad_count = 0
                title = None

            # If title is None or empty, generate a new one based on group
            if not title:
                if group_number == 1:
                    title = f"MuMu {port_id}"  # Fallback to using ID for group 1
                    if int(title.split()[1]) > 40:
                        title = "MuMu 40"  # Max for group 1
                else:
                    # For group 2, start from 41
                    title_num = 40 + port_id
                    if title_num > 80:
                        title_num = 80  # Max for group 2
                    title = f"MuMu {title_num}"

            # Get current date and time
            now = datetime.now()
            current_date = now.strftime("%Y/%m/%d")
            current_time = now.strftime("%H:%M:%S")

            self.cursor.execute(
                """UPDATE ports SET
                   port_number = ?,
                   group_number = ?,
                   connection_status = ?,
                   update_date = ?,
                   update_time = ?,
                   title = ?
                   WHERE id = ?""",
                (int(port_number), group_number, connection_status, current_date, current_time, title, port_id)
            )
            self.conn.commit()
            messagebox.showinfo("موفقیت", "پورت با موفقیت ویرایش شد")

            # Clear entry and reload ports
            self.port_entry.delete(0, tk.END)
            self.load_ports()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطا", "این شماره پورت قبلا ثبت شده است")
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش پورت: {e}")

    def delete_port(self):
        """Delete selected port"""
        selected_items = self.port_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Confirm deletion
        if not messagebox.askyesno("تایید حذف", "آیا از حذف این پورت اطمینان دارید؟"):
            return

        # Get selected item ID
        item = self.port_tree.item(selected_items[0])
        port_id = item['values'][6]  # ID is now at index 6

        # Delete from database
        try:
            self.cursor.execute("DELETE FROM ports WHERE id = ?", (port_id,))
            self.conn.commit()
            messagebox.showinfo("موفقیت", "پورت با موفقیت حذف شد")

            # Clear entry and reload ports
            self.port_entry.delete(0, tk.END)
            self.load_ports()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در حذف پورت: {e}")

    def delete_all_ports(self):
        """Delete all ports from database"""
        # Confirm deletion
        if not messagebox.askyesno("تایید حذف", "آیا از حذف تمام پورت‌ها اطمینان دارید؟"):
            return

        # Delete from database
        try:
            self.cursor.execute("DELETE FROM ports")
            self.conn.commit()
            messagebox.showinfo("موفقیت", "تمام پورت‌ها با موفقیت حذف شدند")

            # Clear entry and reload ports
            self.port_entry.delete(0, tk.END)
            self.load_ports()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در حذف پورت‌ها: {e}")

    def check_adb_availability(self):
        """Check if adb.exe is available"""
        try:
            # Try to run adb version to check if adb is available
            subprocess.run(["adb", "version"], check=True, capture_output=True, text=True)
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            messagebox.showerror("خطا", "adb.exe یافت نشد. لطفا مطمئن شوید که adb در مسیر سیستم قرار دارد.")
            return False

    def check_single_port_connection(self, port_id, port_number):
        """Check connection to a single port using adb.exe"""
        # Try to connect to the port
        connection_status = "نامشخص"

        # First check if the device is already connected
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True)

        # Check if the port is in the list of connected devices
        if f"127.0.0.1:{port_number}" in result.stdout:
            connection_status = "متصل"
        else:
            # Try to connect to the port
            try:
                connect_result = subprocess.run(
                    ["adb", "connect", f"127.0.0.1:{port_number}"],
                    capture_output=True,
                    text=True,
                    timeout=5  # Set a timeout of 5 seconds
                )

                # Check the result
                if "connected" in connect_result.stdout.lower():
                    connection_status = "متصل"
                else:
                    connection_status = "غیرمتصل"
            except subprocess.TimeoutExpired:
                connection_status = "زمان اتصال به پایان رسید"
            except Exception as e:
                connection_status = f"خطا: {str(e)}"

        # Update the connection status in the database
        self.cursor.execute(
            "UPDATE ports SET connection_status = ? WHERE id = ?",
            (connection_status, port_id)
        )
        self.conn.commit()

        return connection_status

    def check_port_connection(self):
        """Check connection to selected port using adb.exe"""
        selected_items = self.port_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port number and ID
        item = self.port_tree.item(selected_items[0])
        port_id = item['values'][6]  # ID is now at index 6
        port_number = item['values'][4]  # Port number is now at index 4

        try:
            # Check if adb is available
            if not self.check_adb_availability():
                return

            # Check connection to the port
            connection_status = self.check_single_port_connection(port_id, port_number)

            # Reload ports to show updated status
            self.load_ports()

            # Show message
            if connection_status == "متصل":
                messagebox.showinfo("بررسی اتصال", f"اتصال به پورت {port_number} برقرار است.")
            else:
                messagebox.showwarning("بررسی اتصال", f"اتصال به پورت {port_number} برقرار نیست.\nوضعیت: {connection_status}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بررسی اتصال: {e}")

    def check_default_group_connections(self):
        """Check connection to all ports in the default group"""
        try:
            # Check if adb is available
            if not self.check_adb_availability():
                return

            # Get the default group
            default_group = self.get_default_group()

            # Get all ports in the default group
            self.cursor.execute(
                "SELECT id, port_number FROM ports WHERE group_number = ?",
                (default_group,)
            )
            ports = self.cursor.fetchall()

            if not ports:
                messagebox.showinfo("اطلاعات", f"هیچ پورتی در گروه {default_group} یافت نشد.")
                return

            # Check connection to each port
            connected_ports = 0
            total_ports = len(ports)

            # Show progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("در حال بررسی اتصال")
            progress_window.geometry("400x150")
            progress_window.transient(self)
            progress_window.grab_set()

            progress_label = ttk.Label(progress_window, text="در حال بررسی اتصال پورت‌ها...")
            progress_label.pack(pady=10)

            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=total_ports)
            progress_bar.pack(fill='x', padx=20, pady=10)

            status_label = ttk.Label(progress_window, text="")
            status_label.pack(pady=10)

            progress_window.update()

            for i, (port_id, port_number) in enumerate(ports):
                # Update progress
                progress_var.set(i)
                status_label.config(text=f"بررسی پورت {port_number} ({i+1} از {total_ports})")
                progress_window.update()

                # Check connection
                connection_status = self.check_single_port_connection(port_id, port_number)

                if connection_status == "متصل":
                    connected_ports += 1

            # Close progress dialog
            progress_window.destroy()

            # Reload ports to show updated status
            self.load_ports()

            # Show result
            messagebox.showinfo("نتیجه بررسی اتصال",
                               f"بررسی اتصال {total_ports} پورت در گروه {default_group} انجام شد.\n"
                               f"{connected_ports} پورت متصل و {total_ports - connected_ports} پورت غیرمتصل است.")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بررسی اتصال: {e}")

    def get_default_port(self, group_number):
        """Get the default port for a specific group"""
        try:
            self.cursor.execute(
                "SELECT port_number FROM ports WHERE group_number = ? AND is_default = 1",
                (group_number,)
            )
            result = self.cursor.fetchone()
            if result:
                return result[0]
            else:
                return None
        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بازیابی پورت پیش‌فرض: {e}")
            return None

    def get_default_group(self):
        """Get the default group from settings (thread-safe)"""
        try:
            # Create a new connection for thread safety
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()
            cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'default_group'")
            result = cursor.fetchone()
            conn.close()

            if result:
                return int(result[0])
            else:
                return 1  # Default to group 1 if not set
        except Exception as e:
            print(f"خطا در بازیابی گروه پیش‌فرض: {e}")
            return 1  # Default to group 1 on error

    def get_stale_data_minutes(self):
        """Get the stale data minutes from settings (thread-safe)"""
        try:
            # Create a new connection for thread safety
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()
            cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'stale_data_minutes'")
            result = cursor.fetchone()
            conn.close()

            if result:
                return int(result[0])
            else:
                return 5  # Default to 5 minutes if not set
        except Exception as e:
            print(f"Error retrieving stale data minutes: {e}")
            return 5  # Default to 5 minutes on error

    def disconnect_single_port(self, port_id, port_number):
        """Disconnect a single port using adb.exe and return the new connection status"""
        try:
            # Try to disconnect the port
            disconnect_result = subprocess.run(
                ["adb", "disconnect", f"127.0.0.1:{port_number}"],
                capture_output=True,
                text=True,
                timeout=5  # Set a timeout of 5 seconds
            )

            # Check the result
            if "disconnected" in disconnect_result.stdout.lower() or "disconnecting" in disconnect_result.stdout.lower():
                connection_status = "غیرمتصل"
            else:
                connection_status = "نامشخص"

            # Update the connection status in the database
            self.cursor.execute(
                "UPDATE ports SET connection_status = ? WHERE id = ?",
                (connection_status, port_id)
            )
            self.conn.commit()

            return connection_status

        except subprocess.TimeoutExpired:
            # Update the connection status in the database
            self.cursor.execute(
                "UPDATE ports SET connection_status = ? WHERE id = ?",
                ("زمان اتصال به پایان رسید", port_id)
            )
            self.conn.commit()
            return "زمان اتصال به پایان رسید"

        except Exception as e:
            # Update the connection status in the database
            error_status = f"خطا: {str(e)}"
            self.cursor.execute(
                "UPDATE ports SET connection_status = ? WHERE id = ?",
                (error_status, port_id)
            )
            self.conn.commit()
            return error_status

    def disconnect_port(self):
        """Disconnect the selected port using adb.exe"""
        selected_items = self.port_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected port number and ID
        item = self.port_tree.item(selected_items[0])
        port_id = item['values'][6]  # ID is now at index 6
        port_number = item['values'][4]  # Port number is now at index 4
        connection_status = item['values'][1]  # Connection status is now at index 1

        # If port is not connected, show a message and return
        if connection_status != "متصل":
            messagebox.showinfo("اطلاعات", f"پورت {port_number} متصل نیست.")
            return

        try:
            # Check if adb is available
            if not self.check_adb_availability():
                return

            # Disconnect the port
            connection_status = self.disconnect_single_port(port_id, port_number)

            # Show message based on the result
            if connection_status == "غیرمتصل":
                messagebox.showinfo("قطع اتصال", f"اتصال به پورت {port_number} با موفقیت قطع شد.")
            else:
                messagebox.showwarning("قطع اتصال", f"قطع اتصال به پورت {port_number} با مشکل مواجه شد.\nوضعیت: {connection_status}")

            # Reload ports to show updated status
            self.load_ports()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در قطع اتصال: {e}")

    def disconnect_all_ports(self):
        """Disconnect all connected ports"""
        try:
            # Check if adb is available
            if not self.check_adb_availability():
                return

            # Get all connected ports
            self.cursor.execute(
                "SELECT id, port_number FROM ports WHERE connection_status = 'متصل'"
            )
            connected_ports = self.cursor.fetchall()

            if not connected_ports:
                messagebox.showinfo("اطلاعات", "هیچ پورت متصلی یافت نشد.")
                return

            # Confirm disconnection
            if not messagebox.askyesno("تایید قطع اتصال", f"آیا از قطع اتصال {len(connected_ports)} پورت متصل اطمینان دارید؟"):
                return

            # Show progress dialog
            progress_window = tk.Toplevel(self)
            progress_window.title("در حال قطع اتصال")
            progress_window.geometry("400x150")
            progress_window.transient(self)
            progress_window.grab_set()

            progress_label = ttk.Label(progress_window, text="در حال قطع اتصال پورت‌ها...")
            progress_label.pack(pady=10)

            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=len(connected_ports))
            progress_bar.pack(fill='x', padx=20, pady=10)

            status_label = ttk.Label(progress_window, text="")
            status_label.pack(pady=10)

            progress_window.update()

            # Disconnect each port
            success_count = 0
            for i, (port_id, port_number) in enumerate(connected_ports):
                # Update progress
                progress_var.set(i)
                status_label.config(text=f"قطع اتصال پورت {port_number} ({i+1} از {len(connected_ports)})")
                progress_window.update()

                # Disconnect the port
                connection_status = self.disconnect_single_port(port_id, port_number)

                if connection_status == "غیرمتصل":
                    success_count += 1

            # Close progress dialog
            progress_window.destroy()

            # Reload ports to show updated status
            self.load_ports()

            # Show result
            messagebox.showinfo("نتیجه قطع اتصال",
                               f"قطع اتصال {len(connected_ports)} پورت انجام شد.\n"
                               f"{success_count} پورت با موفقیت قطع اتصال شدند و {len(connected_ports) - success_count} پورت با مشکل مواجه شدند.")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در قطع اتصال: {e}")

    def show_info_window(self):
        """Show information window with port data"""
        # Create a new toplevel window
        info_window = tk.Toplevel(self)
        info_window.title("نمایش اطلاعات پورت‌ها")
        info_window.geometry("900x600")  # Larger size for better view
        info_window.transient(self)  # Set to be on top of the main window
        info_window.grab_set()  # Modal window

        # Create main frame
        main_frame = ttk.Frame(info_window)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Create filter frame
        filter_frame = ttk.LabelFrame(main_frame, text="فیلتر نمایش")
        filter_frame.pack(fill='x', padx=10, pady=10)

        # Create filter options
        filter_options_frame = ttk.Frame(filter_frame)
        filter_options_frame.pack(fill='x', padx=10, pady=10)

        # Create radio buttons for filter
        self.filter_var = tk.IntVar(value=0)  # 0: All, 1: Group 1, 2: Group 2

        filter_label = ttk.Label(filter_options_frame, text="نمایش:")
        filter_label.pack(side='right', padx=5)

        all_radio = ttk.Radiobutton(filter_options_frame, text="همه",
                                   variable=self.filter_var, value=0,
                                   command=self.refresh_info_treeview)
        all_radio.pack(side='right', padx=5)

        group1_radio = ttk.Radiobutton(filter_options_frame, text="گروه 1",
                                      variable=self.filter_var, value=1,
                                      command=self.refresh_info_treeview)
        group1_radio.pack(side='right', padx=5)

        group2_radio = ttk.Radiobutton(filter_options_frame, text="گروه 2",
                                      variable=self.filter_var, value=2,
                                      command=self.refresh_info_treeview)
        group2_radio.pack(side='right', padx=5)

        # Create delete button
        delete_btn = ttk.Button(filter_options_frame, text="حذف کل اطلاعات",
                               command=self.delete_all_data, width=15)
        delete_btn.pack(side='left', padx=5)

        # Create summary frame
        summary_frame = ttk.LabelFrame(main_frame, text="خلاصه اطلاعات")
        summary_frame.pack(fill='x', padx=10, pady=10)

        # Create summary content
        summary_content = ttk.Frame(summary_frame)
        summary_content.pack(fill='x', padx=10, pady=10)

        # Create total score label
        self.total_score_label = ttk.Label(summary_content, text="مجموع امتیازها: 0")
        self.total_score_label.pack(side='right', padx=5)

        # Create dollar value label
        self.dollar_value_label = ttk.Label(summary_content, text="ارزش به دلار: $0.00")
        self.dollar_value_label.pack(side='right', padx=20)

        # Create treeview frame
        tree_frame = ttk.LabelFrame(main_frame, text="لیست پورت‌ها")
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create Treeview
        columns = ('row', 'port_number', 'group_number', 'is_default', 'connection_status', 'score', 'dollar_value', 'ad_count', 'update_datetime')
        self.info_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')

        # Define headings with sort functionality - right to left order for Persian
        self.info_tree.heading('row', text='ردیف',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'row', False))
        self.info_tree.heading('port_number', text='شماره پورت',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'port_number', False))
        self.info_tree.heading('group_number', text='گروه',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'group_number', False))
        self.info_tree.heading('is_default', text='پیش‌فرض',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'is_default', False))
        self.info_tree.heading('connection_status', text='وضعیت اتصال',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'connection_status', False))
        self.info_tree.heading('score', text='امتیاز',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'score', False))
        self.info_tree.heading('dollar_value', text='ارزش دلاری',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'dollar_value', False))
        self.info_tree.heading('ad_count', text='تعداد تبلیغات',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'ad_count', False))
        self.info_tree.heading('update_datetime', text='تاریخ به‌روزرسانی',
                              command=lambda: self.treeview_sort_column(self.info_tree, 'update_datetime', False))

        # Define columns
        self.info_tree.column('row', width=50, anchor='center')
        self.info_tree.column('port_number', width=100, anchor='center')
        self.info_tree.column('group_number', width=50, anchor='center')
        self.info_tree.column('is_default', width=70, anchor='center')
        self.info_tree.column('connection_status', width=100, anchor='center')
        self.info_tree.column('score', width=100, anchor='center')
        self.info_tree.column('dollar_value', width=80, anchor='center')
        self.info_tree.column('ad_count', width=100, anchor='center')
        self.info_tree.column('update_datetime', width=150, anchor='center')

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.info_tree.yview)
        self.info_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        self.info_tree.pack(fill='both', expand=True, padx=5, pady=5)

        # Load data
        self.refresh_info_treeview()

    def refresh_info_treeview(self):
        """Refresh the info treeview based on the selected filter"""
        # Clear existing items
        for item in self.info_tree.get_children():
            self.info_tree.delete(item)

        # Get filter value
        filter_value = self.filter_var.get()

        # Prepare query based on filter
        if filter_value == 0:  # All
            query = """
                SELECT id, port_number, group_number, is_default, connection_status,
                       score, ad_count, update_date, update_time
                FROM ports
                ORDER BY id
            """
            params = ()
        else:  # Group 1 or 2
            query = """
                SELECT id, port_number, group_number, is_default, connection_status,
                       score, ad_count, update_date, update_time
                FROM ports
                WHERE group_number = ?
                ORDER BY id
            """
            params = (filter_value,)

        try:
            # Execute query
            self.cursor.execute(query, params)
            ports = self.cursor.fetchall()

            # Add to treeview
            row_counter = 1
            total_score = 0

            for port in ports:
                port_values = list(port)

                # Convert is_default to بله/خیر for display
                port_values[3] = "بله" if port[3] == 1 else "خیر"

                # If connection_status is None or empty, set it to "نامشخص"
                if not port_values[4]:
                    port_values[4] = "نامشخص"

                # Format date and time
                update_datetime = f"{port_values[7]} {port_values[8]}" if port_values[7] and port_values[8] else ""

                # Add score to total
                score = port_values[5] if port_values[5] is not None else 0
                total_score += score

                # Format score with 2 decimal places
                score_display = f"{score:.2f}" if score is not None else "0.00"

                # Calculate dollar value (10000 points = $1)
                dollar_value = score / 10000
                dollar_display = f"${dollar_value:.2f}"

                # Create display values
                display_values = (
                    row_counter,            # ردیف
                    port_values[1],         # شماره پورت
                    port_values[2],         # گروه
                    port_values[3],         # پیش‌فرض
                    port_values[4],         # وضعیت اتصال
                    score_display,          # امتیاز (با دو رقم اعشار)
                    dollar_display,         # ارزش دلاری
                    port_values[6],         # تعداد تبلیغات
                    update_datetime         # تاریخ به‌روزرسانی
                )

                self.info_tree.insert('', 'end', values=display_values)
                row_counter += 1

            # Update summary labels
            self.update_summary_labels(total_score)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری اطلاعات: {e}")

    def update_summary_labels(self, total_score):
        """Update summary labels with total score and dollar value"""
        # Format total score with commas and 2 decimal places
        formatted_score = f"{total_score:,.2f}"
        self.total_score_label.config(text=f"مجموع امتیازها: {formatted_score}")

        # Calculate dollar value (10000 points = $1)
        dollar_value = total_score / 10000
        self.dollar_value_label.config(text=f"ارزش به دلار: ${dollar_value:.2f}")

    def delete_all_data(self):
        """Delete all data from the database"""
        # Confirm deletion
        if not messagebox.askyesno("تایید حذف", "آیا از حذف تمام اطلاعات اطمینان دارید؟\nاین عمل غیرقابل بازگشت است."):
            return

        try:
            # Delete all data from ports table
            self.cursor.execute("DELETE FROM ports")
            self.conn.commit()
            messagebox.showinfo("موفقیت", "تمام اطلاعات با موفقیت حذف شدند")

            # Refresh treeview
            self.refresh_info_treeview()

            # Also refresh the main scan treeview
            self.load_default_ports_to_scan_tree()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در حذف اطلاعات: {e}")

    def show_records_window(self):
        """Show records window with daily summaries and port history"""
        # Create a new window
        records_window = tk.Toplevel(self)
        records_window.title("سوابق روزانه")
        records_window.geometry("1000x700")
        records_window.transient(self)  # Set to be on top of the main window
        records_window.grab_set()  # Modal window

        # Create notebook for tabs
        notebook = ttk.Notebook(records_window)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create tabs
        daily_tab = ttk.Frame(notebook)
        port_history_tab = ttk.Frame(notebook)

        notebook.add(daily_tab, text="سوابق روزانه")
        notebook.add(port_history_tab, text="سوابق جزئیات پورت‌ها")

        # ==================== Daily Records Tab ====================
        # Create filter frame
        filter_frame = ttk.LabelFrame(daily_tab, text="فیلترها")
        filter_frame.pack(fill='x', padx=10, pady=5)

        # Create a single row for all filters
        filters_row = ttk.Frame(filter_frame)
        filters_row.pack(fill='x', padx=5, pady=5)

        # Date range filter
        ttk.Label(filters_row, text="از تاریخ:").pack(side='right', padx=5)
        from_date_entry = ttk.Entry(filters_row, width=10)
        from_date_entry.pack(side='right', padx=5)

        ttk.Label(filters_row, text="تا تاریخ:").pack(side='right', padx=5)
        to_date_entry = ttk.Entry(filters_row, width=10)
        to_date_entry.pack(side='right', padx=5)

        # Group filter
        ttk.Label(filters_row, text="گروه:").pack(side='right', padx=5)
        group_var = tk.StringVar(value="all")
        ttk.Radiobutton(filters_row, text="همه", variable=group_var, value="all").pack(side='right', padx=5)
        ttk.Radiobutton(filters_row, text="گروه 1", variable=group_var, value="1").pack(side='right', padx=5)
        ttk.Radiobutton(filters_row, text="گروه 2", variable=group_var, value="2").pack(side='right', padx=5)

        # Apply filter button
        ttk.Button(filters_row, text="اعمال فیلتر", command=lambda: self.load_daily_records(
            daily_tree, group_var.get(), from_date_entry.get(), to_date_entry.get()
        )).pack(side='right', padx=5)

        ttk.Button(filters_row, text="ثبت سوابق امروز",
                  command=lambda: self.record_today_data(daily_tree, group_var.get())
        ).pack(side='right', padx=5)

        # Add summary label for total dollar value
        self.daily_total_dollar_label = ttk.Label(filter_frame, text="مجموع ارزش دلاری: $0.00", anchor="center")
        self.daily_total_dollar_label.pack(side='left', padx=20, pady=5)

        # Create treeview for daily records
        tree_frame = ttk.Frame(daily_tab)
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        daily_tree = ttk.Treeview(tree_frame, columns=('date', 'group', 'total_score', 'total_dollar', 'total_ads', 'avg_ads', 'port_count'))
        daily_tree.heading('date', text='تاریخ',
                          command=lambda: self.treeview_sort_column(daily_tree, 'date', False))
        daily_tree.heading('group', text='گروه',
                          command=lambda: self.treeview_sort_column(daily_tree, 'group', False))
        daily_tree.heading('total_score', text='مجموع امتیاز',
                          command=lambda: self.treeview_sort_column(daily_tree, 'total_score', False))
        daily_tree.heading('total_dollar', text='مجموع دلار',
                          command=lambda: self.treeview_sort_column(daily_tree, 'total_dollar', False))
        daily_tree.heading('total_ads', text='تعداد تبلیغات',
                          command=lambda: self.treeview_sort_column(daily_tree, 'total_ads', False))
        daily_tree.heading('avg_ads', text='میانگین تبلیغات',
                          command=lambda: self.treeview_sort_column(daily_tree, 'avg_ads', False))
        daily_tree.heading('port_count', text='تعداد پورت',
                          command=lambda: self.treeview_sort_column(daily_tree, 'port_count', False))

        daily_tree.column('#0', width=0, stretch=tk.NO)
        daily_tree.column('date', width=100, anchor='center')
        daily_tree.column('group', width=50, anchor='center')
        daily_tree.column('total_score', width=150, anchor='center')
        daily_tree.column('total_dollar', width=100, anchor='center')
        daily_tree.column('total_ads', width=100, anchor='center')
        daily_tree.column('avg_ads', width=100, anchor='center')
        daily_tree.column('port_count', width=100, anchor='center')

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=daily_tree.yview)
        daily_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        daily_tree.pack(fill='both', expand=True)

        # ==================== Port History Tab ====================
        # Create filter frame
        port_filter_frame = ttk.LabelFrame(port_history_tab, text="فیلترها")
        port_filter_frame.pack(fill='x', padx=10, pady=5)

        # Create a single row for all filters
        port_filters_row = ttk.Frame(port_filter_frame)
        port_filters_row.pack(fill='x', padx=5, pady=5)

        # Group filter for port history
        ttk.Label(port_filters_row, text="گروه:").pack(side='right', padx=5)
        port_group_var = tk.StringVar(value="all")
        ttk.Radiobutton(port_filters_row, text="همه", variable=port_group_var, value="all").pack(side='right', padx=5)
        ttk.Radiobutton(port_filters_row, text="گروه 1", variable=port_group_var, value="1").pack(side='right', padx=5)
        ttk.Radiobutton(port_filters_row, text="گروه 2", variable=port_group_var, value="2").pack(side='right', padx=5)

        # Date filter
        ttk.Label(port_filters_row, text="تاریخ:").pack(side='right', padx=5)
        port_date_entry = ttk.Entry(port_filters_row, width=10)
        port_date_entry.pack(side='right', padx=5)

        # Port filter
        ttk.Label(port_filters_row, text="شماره پورت:").pack(side='right', padx=5)
        port_number_entry = ttk.Entry(port_filters_row, width=10)
        port_number_entry.pack(side='right', padx=5)

        # Apply filter button
        ttk.Button(port_filters_row, text="اعمال فیلتر", command=lambda: self.load_port_history(
            port_history_tree, port_group_var.get(), port_date_entry.get(), port_number_entry.get()
        )).pack(side='right', padx=5)

        # Add summary label for total dollar value
        self.port_total_dollar_label = ttk.Label(port_filter_frame, text="مجموع ارزش دلاری: $0.00", anchor="center")
        self.port_total_dollar_label.pack(side='left', padx=20, pady=5)

        # Create treeview for port history
        port_tree_frame = ttk.Frame(port_history_tab)
        port_tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        port_history_tree = ttk.Treeview(port_tree_frame,
                                        columns=('date', 'port_number', 'group', 'score', 'dollar', 'ad_count'))
        port_history_tree.heading('date', text='تاریخ',
                                 command=lambda: self.treeview_sort_column(port_history_tree, 'date', False))
        port_history_tree.heading('port_number', text='شماره پورت',
                                 command=lambda: self.treeview_sort_column(port_history_tree, 'port_number', False))
        port_history_tree.heading('group', text='گروه',
                                 command=lambda: self.treeview_sort_column(port_history_tree, 'group', False))
        port_history_tree.heading('score', text='امتیاز',
                                 command=lambda: self.treeview_sort_column(port_history_tree, 'score', False))
        port_history_tree.heading('dollar', text='دلار',
                                 command=lambda: self.treeview_sort_column(port_history_tree, 'dollar', False))
        port_history_tree.heading('ad_count', text='تعداد تبلیغات',
                                 command=lambda: self.treeview_sort_column(port_history_tree, 'ad_count', False))

        port_history_tree.column('#0', width=0, stretch=tk.NO)
        port_history_tree.column('date', width=100, anchor='center')
        port_history_tree.column('port_number', width=100, anchor='center')
        port_history_tree.column('group', width=50, anchor='center')
        port_history_tree.column('score', width=150, anchor='center')
        port_history_tree.column('dollar', width=100, anchor='center')
        port_history_tree.column('ad_count', width=100, anchor='center')

        # Add scrollbar
        port_scrollbar = ttk.Scrollbar(port_tree_frame, orient='vertical', command=port_history_tree.yview)
        port_history_tree.configure(yscrollcommand=port_scrollbar.set)
        port_scrollbar.pack(side='right', fill='y')
        port_history_tree.pack(fill='both', expand=True)

        # Load initial data
        try:
            self.load_daily_records(daily_tree, "all", "", "")
            self.load_port_history(port_history_tree, "all", "", "")
        except Exception as e:
            print(f"Error loading initial data: {e}")
            messagebox.showerror("خطا", f"خطا در بارگذاری اطلاعات اولیه: {e}")

    def load_daily_records(self, tree, group_filter, from_date, to_date):
        """Load daily records based on filters"""
        # Clear existing data
        for item in tree.get_children():
            tree.delete(item)

        try:
            # Print debug info
            print(f"Loading daily records with filter: group={group_filter}, from_date={from_date}, to_date={to_date}")

            # Check if daily_records table exists
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='daily_records'")
            table_exists = self.cursor.fetchone() is not None

            if not table_exists:
                print("daily_records table does not exist!")
                tree.insert('', 'end', values=("جدول سوابق وجود ندارد", "", "", "", "", "", ""))
                return

            # Build query based on filters
            query = "SELECT * FROM daily_records WHERE 1=1"
            params = []

            if group_filter != "all":
                query += " AND group_number = ?"
                params.append(int(group_filter))

            if from_date:
                query += " AND record_date >= ?"
                params.append(from_date)

            if to_date:
                query += " AND record_date <= ?"
                params.append(to_date)

            query += " ORDER BY record_date DESC"

            # Print query for debugging
            print(f"Query: {query}, Params: {params}")

            # Execute query
            self.cursor.execute(query, params)
            records = self.cursor.fetchall()

            print(f"Found {len(records)} records")

            # Insert data into treeview and calculate total dollar value
            total_dollar_value = 0.0

            for record in records:
                record_id, date, group, total_score, total_dollar, total_ads, avg_ads, port_count = record

                # Add to total dollar value
                try:
                    total_dollar_value += float(total_dollar)
                except (ValueError, TypeError):
                    pass

                # Format numbers
                try:
                    formatted_score = f"{float(total_score):,.2f}"
                except (ValueError, TypeError):
                    formatted_score = "0.00"

                try:
                    formatted_dollar = f"${float(total_dollar):.2f}"
                except (ValueError, TypeError):
                    formatted_dollar = "$0.00"

                try:
                    formatted_avg = f"{float(avg_ads):.2f}"
                except (ValueError, TypeError):
                    formatted_avg = "0.00"

                tree.insert('', 'end', values=(
                    date,
                    f"گروه {group}",
                    formatted_score,
                    formatted_dollar,
                    total_ads,
                    formatted_avg,
                    port_count
                ))

            # Update total dollar value label
            self.daily_total_dollar_label.config(text=f"مجموع ارزش دلاری: ${total_dollar_value:.2f}")

            # If no records found, show message
            if not records:
                tree.insert('', 'end', values=("--", "--", "--", "--", "--", "--", "--"))
                print("No records found")

        except Exception as e:
            print(f"Error loading daily records: {e}")
            messagebox.showerror("خطا", f"خطا در بارگذاری سوابق روزانه: {e}")
            tree.insert('', 'end', values=("خطا", "", "", "", "", "", ""))

    def load_port_history(self, tree, group_filter, date_filter, port_filter):
        """Load port history based on filters"""
        # Clear existing data
        for item in tree.get_children():
            tree.delete(item)

        try:
            # Print debug info
            print(f"Loading port history with filter: group={group_filter}, date={date_filter}, port={port_filter}")

            # Check if port_history table exists
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='port_history'")
            table_exists = self.cursor.fetchone() is not None

            if not table_exists:
                print("port_history table does not exist!")
                tree.insert('', 'end', values=("جدول سوابق پورت‌ها وجود ندارد", "", "", "", "", ""))
                return

            # Build query based on filters
            query = "SELECT * FROM port_history WHERE 1=1"
            params = []

            if group_filter != "all":
                query += " AND group_number = ?"
                params.append(int(group_filter))

            if date_filter:
                query += " AND record_date = ?"
                params.append(date_filter)

            if port_filter:
                try:
                    query += " AND port_number = ?"
                    params.append(int(port_filter))
                except ValueError:
                    print(f"Invalid port number: {port_filter}")
                    messagebox.showwarning("هشدار", "شماره پورت باید عدد باشد.")

            query += " ORDER BY record_date DESC, port_number ASC"

            # Print query for debugging
            print(f"Query: {query}, Params: {params}")

            # Execute query
            self.cursor.execute(query, params)
            records = self.cursor.fetchall()

            print(f"Found {len(records)} port history records")

            # Insert data into treeview and calculate total dollar value
            total_dollar_value = 0.0

            for record in records:
                record_id, date, port_id, port_number, group, score, dollar, ad_count = record

                # Add to total dollar value
                try:
                    total_dollar_value += float(dollar)
                except (ValueError, TypeError):
                    pass

                # Format numbers
                try:
                    formatted_score = f"{float(score):,.2f}"
                except (ValueError, TypeError):
                    formatted_score = "0.00"

                try:
                    formatted_dollar = f"${float(dollar):.2f}"
                except (ValueError, TypeError):
                    formatted_dollar = "$0.00"

                tree.insert('', 'end', values=(
                    date,
                    port_number,
                    f"گروه {group}",
                    formatted_score,
                    formatted_dollar,
                    ad_count
                ))

            # Update total dollar value label
            self.port_total_dollar_label.config(text=f"مجموع ارزش دلاری: ${total_dollar_value:.2f}")

            # If no records found, show message
            if not records:
                tree.insert('', 'end', values=("--", "--", "--", "--", "--", "--"))
                print("No port history records found")

        except Exception as e:
            print(f"Error loading port history: {e}")
            messagebox.showerror("خطا", f"خطا در بارگذاری سوابق پورت‌ها: {e}")
            tree.insert('', 'end', values=("خطا", "", "", "", "", ""))

    def record_today_data(self, tree, group_filter):
        """Record today's data to daily_records and port_history tables (manual trigger)"""
        try:
            # Get current date in the format YYYY/MM/DD
            now = datetime.now()
            current_time = now.strftime("%H:%M:%S")

            # Check if we need to use yesterday's date based on the 15:30 rule
            if now.hour < 15 or (now.hour == 15 and now.minute < 30):
                # Before 15:30, use yesterday's date
                yesterday = now - timedelta(days=1)
                record_date = yesterday.strftime("%Y/%m/%d")
            else:
                # After 15:30, use today's date
                record_date = now.strftime("%Y/%m/%d")

            # Check if we already have records for this date
            self.cursor.execute("SELECT COUNT(*) FROM daily_records WHERE record_date = ?", (record_date,))
            count = self.cursor.fetchone()[0]

            if count > 0:
                # Ask user if they want to overwrite
                if not messagebox.askyesno("تایید",
                                         f"سوابق برای تاریخ {record_date} قبلاً ثبت شده است. آیا می‌خواهید آن‌ها را بازنویسی کنید؟"):
                    return

                # Delete existing records for this date
                self.cursor.execute("DELETE FROM daily_records WHERE record_date = ?", (record_date,))
                self.cursor.execute("DELETE FROM port_history WHERE record_date = ?", (record_date,))

            # Update records using the common function
            self.update_daily_records(record_date, group_filter)

            # Reload data in the treeview if provided
            if tree:
                self.load_daily_records(tree, group_filter, "", "")

            # Show success message
            messagebox.showinfo("موفقیت", f"سوابق برای تاریخ {record_date} با موفقیت ثبت شدند.")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ثبت سوابق: {e}")

    def check_day_change(self):
        """Check if the day has changed (crossed 15:30) since the last record update"""
        try:
            # Create a new connection to avoid thread issues
            conn = sqlite3.connect('ports.db')
            cursor = conn.cursor()

            # Get the last record time from settings
            cursor.execute("SELECT setting_value FROM settings WHERE setting_name = 'last_record_time'")
            result = cursor.fetchone()

            # Close the connection
            conn.close()

            if not result:
                # No last record time, assume first run
                return False

            last_record_time_str = result[0]
            last_record_time = datetime.strptime(last_record_time_str, "%Y-%m-%d %H:%M:%S")

            # Get current time
            now = datetime.now()

            # Check if we've crossed the 15:30 boundary
            # Case 1: Last record was before 15:30, now is after 15:30 on the same day
            if (last_record_time.hour < 15 or (last_record_time.hour == 15 and last_record_time.minute < 30)) and \
               (now.hour > 15 or (now.hour == 15 and now.minute >= 30)) and \
               last_record_time.date() == now.date():
                return True

            # Case 2: Last record was on a different day
            if last_record_time.date() != now.date():
                return True

            # No day change detected
            return False

        except Exception as e:
            print(f"Error checking day change: {e}")
            return False

    def update_daily_records(self, record_date=None, group_filter="all"):
        """Update daily records automatically (called after each scan)"""
        try:
            # This function is now called in the main thread, so we can use self.conn and self.cursor safely

            # Check if day has changed
            day_changed = self.check_day_change()

            # If no date provided, determine the current record date
            if record_date is None:
                now = datetime.now()

                # Check if we need to use yesterday's date based on the 15:30 rule
                if now.hour < 15 or (now.hour == 15 and now.minute < 30):
                    # Before 15:30, use yesterday's date
                    yesterday = now - timedelta(days=1)
                    record_date = yesterday.strftime("%Y/%m/%d")
                else:
                    # After 15:30, use today's date
                    record_date = now.strftime("%Y/%m/%d")

            # If day has changed, log it
            if day_changed:
                print(f"Day changed! New record date: {record_date}")
                # We could add additional logic here if needed

            print(f"Updating daily records for date: {record_date}, group filter: {group_filter}")

            # Process each group
            groups_to_process = []
            if group_filter == "all":
                groups_to_process = [1, 2]
            else:
                groups_to_process = [int(group_filter)]

            for group in groups_to_process:
                # Get all ports in this group
                self.cursor.execute(
                    "SELECT id, port_number, score, ad_count FROM ports WHERE group_number = ?",
                    (group,)
                )
                ports = self.cursor.fetchall()

                if not ports:
                    continue

                # Calculate totals
                total_score = 0
                total_ads = 0
                port_count = len(ports)

                # Check if we already have records for this date and group
                self.cursor.execute(
                    "SELECT id FROM daily_records WHERE record_date = ? AND group_number = ?",
                    (record_date, group)
                )
                daily_record = self.cursor.fetchone()

                # Print debug info
                if daily_record:
                    print(f"Found existing daily record for date {record_date}, group {group}, id: {daily_record[0]}")
                else:
                    print(f"No existing daily record for date {record_date}, group {group}, will create new")

                # Check if we need to delete existing port history records
                if daily_record:
                    # Delete existing port history records for this date and group
                    self.cursor.execute(
                        "DELETE FROM port_history WHERE record_date = ? AND group_number = ?",
                        (record_date, group)
                    )
                    print(f"Deleted existing port history records for date {record_date}, group {group}")

                # Record individual port history
                for port_id, port_number, score, ad_count in ports:
                    # Skip ports with no data
                    if score is None:
                        score = 0
                    if ad_count is None:
                        ad_count = 0

                    # Add to totals
                    total_score += score
                    total_ads += ad_count

                    # Calculate dollar value
                    dollar_value = score / 10000

                    # Print debug info
                    print(f"Recording port history: date={record_date}, port_id={port_id}, port_number={port_number}, score={score}, dollar={dollar_value}, ads={ad_count}")

                    try:
                        # Insert into port_history
                        self.cursor.execute(
                            """INSERT INTO port_history
                               (record_date, port_id, port_number, group_number, score, dollar_value, ad_count)
                               VALUES (?, ?, ?, ?, ?, ?, ?)""",
                            (record_date, port_id, port_number, group, score, dollar_value, ad_count)
                        )
                    except sqlite3.IntegrityError as e:
                        print(f"IntegrityError for port {port_id}: {e}")
                        # Try to update instead
                        self.cursor.execute(
                            """UPDATE port_history
                               SET score = ?, dollar_value = ?, ad_count = ?
                               WHERE record_date = ? AND port_id = ?""",
                            (score, dollar_value, ad_count, record_date, port_id)
                        )
                        print(f"Updated existing port history record for port {port_id}")
                    except Exception as e:
                        print(f"Error recording port history for port {port_id}: {e}")

                # Calculate averages and dollar value
                avg_ads = total_ads / port_count if port_count > 0 else 0
                total_dollar = total_score / 10000

                # Print debug info
                print(f"Daily record summary: date={record_date}, group={group}, total_score={total_score}, total_dollar={total_dollar}, total_ads={total_ads}, avg_ads={avg_ads}, port_count={port_count}")

                # Update or insert daily_records
                try:
                    if daily_record:
                        # Update existing record
                        self.cursor.execute(
                            """UPDATE daily_records
                               SET total_score = ?,
                                   total_dollar = ?,
                                   total_ads = ?,
                                   avg_ads = ?,
                                   port_count = ?
                               WHERE record_date = ? AND group_number = ?""",
                            (total_score, total_dollar, total_ads, avg_ads, port_count, record_date, group)
                        )
                        print(f"Updated existing daily record for date {record_date}, group {group}")
                    else:
                        # Insert new record
                        self.cursor.execute(
                            """INSERT INTO daily_records
                               (record_date, group_number, total_score, total_dollar, total_ads, avg_ads, port_count)
                               VALUES (?, ?, ?, ?, ?, ?, ?)""",
                            (record_date, group, total_score, total_dollar, total_ads, avg_ads, port_count)
                        )
                        print(f"Inserted new daily record for date {record_date}, group {group}")
                except Exception as e:
                    print(f"Error updating/inserting daily record for date {record_date}, group {group}: {e}")

            # Commit changes
            self.conn.commit()

            # Update the last record time
            now = datetime.now()
            try:
                self.cursor.execute(
                    "UPDATE settings SET setting_value = ? WHERE setting_name = 'last_record_time'",
                    (now.strftime("%Y-%m-%d %H:%M:%S"),)
                )
                self.conn.commit()
                print(f"Updated last_record_time to {now.strftime('%Y-%m-%d %H:%M:%S')}")
            except Exception as e:
                print(f"Error updating last_record_time: {e}")

            return True

        except Exception as e:
            print(f"Error updating daily records: {e}")
            return False

    def show_forecast_window(self):
        """Show forecast window to predict when ports will reach 2 dollars"""
        # Create a new toplevel window
        forecast_window = tk.Toplevel(self)
        forecast_window.title("پیش‌بینی رسیدن به آستانه 2 دلار")
        forecast_window.geometry("900x600")
        forecast_window.transient(self)  # Set to be on top of the main window
        forecast_window.grab_set()  # Modal window

        # Create main frame
        main_frame = ttk.Frame(forecast_window)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Create filter frame
        filter_frame = ttk.LabelFrame(main_frame, text="تنظیمات پیش‌بینی")
        filter_frame.pack(fill='x', padx=10, pady=10)

        # Create filter options
        filter_options_frame = ttk.Frame(filter_frame)
        filter_options_frame.pack(fill='x', padx=10, pady=10)

        # Days input
        days_frame = ttk.Frame(filter_options_frame)
        days_frame.pack(side='right', padx=10)

        days_label = ttk.Label(days_frame, text="تعداد روز:")
        days_label.pack(side='right', padx=5)

        # Function to trigger calculation when days change
        def on_days_change(*args):
            # Only calculate if the value is valid
            value = days_entry.get()
            if value.isdigit() and 1 <= int(value) <= 99:
                calculate_forecast()

        # Validate function for numeric input
        def validate_number(action, value_if_allowed):
            if action == '0':  # Delete
                return True
            if value_if_allowed == "":
                return True
            if value_if_allowed.isdigit() and 1 <= int(value_if_allowed) <= 99:
                return True
            return False

        validate_cmd = forecast_window.register(validate_number)

        days_entry = ttk.Entry(days_frame, width=5, validate="key",
                              validatecommand=(validate_cmd, '%d', '%P'))
        days_entry.insert(0, "10")  # Default value
        days_entry.pack(side='right', padx=5)

        # Bind events to trigger calculation
        days_entry.bind("<Return>", on_days_change)  # Enter key
        days_entry.bind("<FocusOut>", on_days_change)  # When focus leaves
        days_entry.bind("<KeyRelease>", on_days_change)  # When key is released

        # Dollar threshold filter
        dollar_frame = ttk.Frame(filter_options_frame)
        dollar_frame.pack(side='right', padx=10)

        dollar_label = ttk.Label(dollar_frame, text="آستانه دلار:")
        dollar_label.pack(side='right', padx=5)

        dollar_var = tk.IntVar(value=2)  # Default: 2 dollars

        # Function to trigger calculation when dollar threshold changes
        def on_dollar_change():
            calculate_forecast()

        dollar2_radio = ttk.Radiobutton(dollar_frame, text="2$",
                                      variable=dollar_var, value=2,
                                      command=on_dollar_change)
        dollar2_radio.pack(side='right', padx=5)

        dollar3_radio = ttk.Radiobutton(dollar_frame, text="3$",
                                      variable=dollar_var, value=3,
                                      command=on_dollar_change)
        dollar3_radio.pack(side='right', padx=5)

        dollar4_radio = ttk.Radiobutton(dollar_frame, text="4$",
                                      variable=dollar_var, value=4,
                                      command=on_dollar_change)
        dollar4_radio.pack(side='right', padx=5)

        dollar5_radio = ttk.Radiobutton(dollar_frame, text="5$",
                                      variable=dollar_var, value=5,
                                      command=on_dollar_change)
        dollar5_radio.pack(side='right', padx=5)

        # Group filter
        group_frame = ttk.Frame(filter_options_frame)
        group_frame.pack(side='right', padx=20)

        group_label = ttk.Label(group_frame, text="گروه:")
        group_label.pack(side='right', padx=5)

        group_var = tk.IntVar(value=0)  # 0: All, 1: Group 1, 2: Group 2

        # Function to trigger calculation when group changes
        def on_group_change():
            calculate_forecast()

        all_radio = ttk.Radiobutton(group_frame, text="همه",
                                   variable=group_var, value=0,
                                   command=on_group_change)
        all_radio.pack(side='right', padx=5)

        group1_radio = ttk.Radiobutton(group_frame, text="گروه 1",
                                      variable=group_var, value=1,
                                      command=on_group_change)
        group1_radio.pack(side='right', padx=5)

        group2_radio = ttk.Radiobutton(group_frame, text="گروه 2",
                                      variable=group_var, value=2,
                                      command=on_group_change)
        group2_radio.pack(side='right', padx=5)

        # Create treeview frame
        tree_frame = ttk.LabelFrame(main_frame, text="پیش‌بینی رسیدن به آستانه دلار")
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create Treeview with separate columns for group 1 and group 2 ports
        columns = ('day', 'count', 'group1_ports', 'group2_ports')
        forecast_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')

        # Define headings with sort functionality
        forecast_tree.heading('day', text='روز',
                             command=lambda: self.treeview_sort_column(forecast_tree, 'day', False))
        forecast_tree.heading('count', text='تعداد',
                             command=lambda: self.treeview_sort_column(forecast_tree, 'count', False))
        forecast_tree.heading('group1_ports', text='پورت‌های گروه 1',
                             command=lambda: self.treeview_sort_column(forecast_tree, 'group1_ports', False))
        forecast_tree.heading('group2_ports', text='پورت‌های گروه 2',
                             command=lambda: self.treeview_sort_column(forecast_tree, 'group2_ports', False))

        # Define columns
        forecast_tree.column('day', width=80, anchor='center')
        forecast_tree.column('count', width=60, anchor='center')
        forecast_tree.column('group1_ports', width=350, anchor='w')
        forecast_tree.column('group2_ports', width=350, anchor='w')

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=forecast_tree.yview)
        forecast_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        forecast_tree.pack(fill='both', expand=True, padx=5, pady=5)

        # Function to calculate and update forecast
        def calculate_forecast():
            # Clear existing items
            for item in forecast_tree.get_children():
                forecast_tree.delete(item)

            try:
                # Get days from entry
                days_limit = int(days_entry.get())
                if days_limit < 1:
                    days_limit = 10  # Default to 10 if invalid

                # Get selected group
                selected_group = group_var.get()

                # Get selected dollar threshold
                selected_dollar = dollar_var.get()
                dollar_threshold = selected_dollar * 10000  # Convert to points (1 dollar = 10000 points)

                # Update tree frame title
                tree_frame.configure(text=f"پیش‌بینی رسیدن به آستانه {selected_dollar} دلار")

                # Prepare query based on group selection
                if selected_group == 0:  # All
                    query = """
                        SELECT port_number, score, group_number, title
                        FROM ports
                        ORDER BY id
                    """
                    params = ()
                else:  # Group 1 or 2
                    query = """
                        SELECT port_number, score, group_number, title
                        FROM ports
                        WHERE group_number = ?
                        ORDER BY id
                    """
                    params = (selected_group,)

                # Get ports data
                self.cursor.execute(query, params)
                ports_data = self.cursor.fetchall()

                # Dictionaries to store forecast days for each group
                forecast_days = {}
                group1_ports = {}
                group2_ports = {}

                # Daily growth rate (1000 points per day)
                daily_growth = 1000

                # Calculate days to reach the dollar threshold for each port
                for port_number, score, group_number, title in ports_data:
                    # Skip ports that already have reached the threshold
                    if score >= dollar_threshold:
                        continue

                    # Calculate days needed to reach the threshold
                    points_needed = dollar_threshold - score
                    days_needed = math.ceil(points_needed / daily_growth)

                    # Skip if days needed is greater than the limit
                    if days_needed > days_limit:
                        continue

                    # Extract just the number from the title (e.g., "MuMu 45" -> "45")
                    if title and "MuMu" in title:
                        # Extract the number after "MuMu "
                        try:
                            port_display = title.split("MuMu ")[1]
                        except (IndexError, ValueError):
                            port_display = str(port_number)
                    else:
                        port_display = str(port_number)

                    # Add to forecast dictionary
                    if days_needed not in forecast_days:
                        forecast_days[days_needed] = 0
                        group1_ports[days_needed] = []
                        group2_ports[days_needed] = []

                    # Increment count
                    forecast_days[days_needed] += 1

                    # Add to appropriate group list
                    if group_number == 1:
                        group1_ports[days_needed].append(port_display)
                    elif group_number == 2:
                        group2_ports[days_needed].append(port_display)

                # Sort days
                sorted_days = sorted(forecast_days.keys())

                # Add to treeview
                for day in sorted_days:
                    group1_list = ", ".join(group1_ports[day]) if day in group1_ports else ""
                    group2_list = ", ".join(group2_ports[day]) if day in group2_ports else ""
                    ports_count = forecast_days[day]  # Count of ports for this day
                    forecast_tree.insert('', 'end', values=(f"{day} روز", ports_count, group1_list, group2_list))

                # If no ports found, show message
                if not sorted_days:
                    forecast_tree.insert('', 'end', values=("--", "0", "هیچ پورتی یافت نشد", "هیچ پورتی یافت نشد"))

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در محاسبه پیش‌بینی: {e}")
                forecast_tree.insert('', 'end', values=("خطا", "0", str(e), str(e)))

        # No calculate button needed anymore

        # Add close button
        close_button = ttk.Button(
            main_frame,
            text="بستن",
            command=forecast_window.destroy,
            width=15
        )
        close_button.pack(pady=10)

        # Initial calculation
        calculate_forecast()

    def set_port_as_default(self):
        """Toggle default status of the selected port"""
        selected_items = self.port_tree.selection()
        if not selected_items:
            messagebox.showwarning("هشدار", "لطفا یک پورت را از لیست انتخاب کنید")
            return

        # Get selected item ID and group
        item = self.port_tree.item(selected_items[0])
        port_id = item['values'][6]  # ID is now at index 6
        is_default = item['values'][2] == "بله"  # Current default status is now at index 2

        try:
            # Toggle default status
            new_status = 0 if is_default else 1
            status_text = "غیر پیش‌فرض" if is_default else "پیش‌فرض"

            # Update the port's default status
            self.cursor.execute(
                "UPDATE ports SET is_default = ? WHERE id = ?",
                (new_status, port_id)
            )

            self.conn.commit()
            messagebox.showinfo("موفقیت", f"پورت انتخاب شده به عنوان {status_text} تنظیم شد")

            # Reload ports to show changes
            self.load_ports()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در تغییر وضعیت پیش‌فرض پورت: {e}")

    def detect_and_click_ad_speaker(self, port_id, port_number, screenshot_path):
        """Detect and click speaker icon for ad play in the bottom area of the screen"""
        try:
            from PIL import Image
            import numpy as np
            import pytesseract

            # Load the screenshot
            image = Image.open(screenshot_path)

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Convert to numpy array for analysis
            img_array = np.array(image)
            height, width = img_array.shape[:2]

            print(f"🔊 تحلیل تصویر برای تشخیص ایکن بلندگو: {width}x{height}")

            # STEP 1: Check if this is the correct screen by looking for key elements
            print("🔍 مرحله 1: تشخیص صفحه مناسب...")

            # Look for "PLAY AN ADVERT TO GET CROWNS" text in the bottom area
            bottom_area = image.crop((0, int(height * 0.7), width, height))  # Bottom 30% of screen

            try:
                # Use OCR to detect the text
                custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz '
                ocr_text = pytesseract.image_to_string(bottom_area, config=custom_config).strip().lower()
                print(f"متن تشخیص داده شده در پایین صفحه: '{ocr_text}'")

                # Check if this is the ad play screen
                if "play" in ocr_text and ("advert" in ocr_text or "ad" in ocr_text) and "crown" in ocr_text:
                    print("✅ صفحه پخش تبلیغات تشخیص داده شد!")
                else:
                    print("❌ این صفحه مناسب پخش تبلیغات نیست")
                    return False

            except Exception as ocr_error:
                print(f"خطا در OCR: {ocr_error}")
                print("ادامه با تشخیص رنگی...")

            # STEP 2: Look for speaker icon in the bottom area
            print("🎯 مرحله 2: جستجوی ایکن بلندگو...")

            # Focus on bottom area where speaker icon should be
            bottom_region = img_array[int(height * 0.75):height, :]  # Bottom 25% of screen
            bottom_height, bottom_width = bottom_region.shape[:2]

            print(f"ناحیه جستجو: {bottom_width}x{bottom_height} (پایین صفحه)")

            # Define color ranges for speaker icon (based on your images)
            speaker_color_ranges = {
                'speaker_gray': ([160, 160, 160], [200, 200, 200]),   # Speaker gray color
                'speaker_light': ([200, 200, 200], [240, 240, 240]), # Light speaker
                'speaker_white': ([220, 220, 220], [255, 255, 255]), # White parts of speaker
                'speaker_dark': ([120, 120, 120], [170, 170, 170]),  # Dark parts of speaker
            }

            speaker_candidates = []

            # Look for speaker icon patterns
            for color_name, (lower, upper) in speaker_color_ranges.items():
                lower = np.array(lower)
                upper = np.array(upper)

                # Create mask for this color in the bottom region
                color_mask = np.all((bottom_region >= lower) & (bottom_region <= upper), axis=2)
                color_pixels = np.sum(color_mask)

                if color_pixels > 50:  # Minimum threshold for speaker icon
                    # Find center of color pixels
                    color_positions = np.where(color_mask)
                    if len(color_positions[0]) > 0:
                        rel_y = int(np.mean(color_positions[0]))
                        rel_x = int(np.mean(color_positions[1]))

                        # Convert to absolute coordinates
                        abs_x = rel_x
                        abs_y = int(height * 0.75) + rel_y

                        speaker_candidates.append({
                            'color': color_name,
                            'pixels': color_pixels,
                            'position': (abs_x, abs_y),
                            'relative_position': (rel_x, rel_y)
                        })
                        print(f"کاندید بلندگو {color_name}: {color_pixels} پیکسل در موقعیت ({abs_x}, {abs_y})")

            # STEP 3: Look for specific speaker icon area (left side of bottom area)
            # Based on your images, speaker icon is on the left side with number "5" next to it
            print("🔍 مرحله 3: جستجوی ناحیه مخصوص بلندگو...")

            # Focus on left side of bottom area where speaker typically appears
            left_bottom_region = bottom_region[:, :int(bottom_width * 0.4)]  # Left 40% of bottom area

            # Look for the number "5" which appears next to the speaker
            try:
                left_bottom_image = Image.fromarray(left_bottom_region)
                number_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                number_text = pytesseract.image_to_string(left_bottom_image, config=number_config).strip()
                print(f"عدد تشخیص داده شده کنار بلندگو: '{number_text}'")

                if "5" in number_text:
                    print("✅ عدد '5' کنار بلندگو تشخیص داده شد!")

                    # Find position of number 5 and estimate speaker position
                    # Speaker is typically to the left of the number
                    number_positions = np.where(left_bottom_region > 200)  # Look for bright pixels (number)
                    if len(number_positions[0]) > 0:
                        number_y = int(np.mean(number_positions[0]))
                        number_x = int(np.mean(number_positions[1]))

                        # Estimate speaker position (to the left of number)
                        speaker_x = max(0, number_x - 50)  # 50 pixels to the left
                        speaker_y = number_y

                        # Convert to absolute coordinates
                        abs_speaker_x = speaker_x
                        abs_speaker_y = int(height * 0.75) + speaker_y

                        speaker_candidates.append({
                            'color': 'estimated_from_number',
                            'pixels': 999,  # High priority
                            'position': (abs_speaker_x, abs_speaker_y),
                            'relative_position': (speaker_x, speaker_y)
                        })
                        print(f"موقعیت تخمینی بلندگو بر اساس عدد 5: ({abs_speaker_x}, {abs_speaker_y})")

            except Exception as number_error:
                print(f"خطا در تشخیص عدد: {number_error}")

            # STEP 4: Filter candidates to left side only and select best
            print("🎯 مرحله 4: فیلتر کردن کاندیدها به سمت چپ...")

            # Filter candidates to only those in the left side of the screen
            left_side_candidates = []
            for candidate in speaker_candidates:
                x, y = candidate['position']
                # Only consider candidates in the left 50% of the screen (expanded from 30% to include speaker area)
                if x < width * 0.5:
                    left_side_candidates.append(candidate)
                    print(f"کاندید سمت چپ: {candidate['color']} در ({x}, {y})")
                else:
                    print(f"کاندید حذف شده (خارج از سمت چپ): {candidate['color']} در ({x}, {y})")

            # If no left-side candidates, use multiple fixed positions based on your images
            if not left_side_candidates:
                print("⚠️ هیچ کاندید سمت چپی یافت نشد - استفاده از موقعیت‌های ثابت")

                # Based on your crop images, try multiple potential speaker positions (corrected to left side)
                potential_positions = [
                    (int(width * 0.43), int(height * 0.92)),  # 43% from left (230/540), 92% from top
                    (int(width * 0.41), int(height * 0.91)),  # 41% from left, 91% from top
                    (int(width * 0.45), int(height * 0.93)),  # 45% from left, 93% from top
                    (int(width * 0.39), int(height * 0.90)),  # 39% from left, 90% from top
                ]

                for i, (fixed_x, fixed_y) in enumerate(potential_positions):
                    left_side_candidates.append({
                        'color': f'fixed_position_{i+1}',
                        'pixels': 500 - i*10,  # Decreasing priority
                        'position': (fixed_x, fixed_y),
                        'relative_position': (fixed_x, fixed_y - int(height * 0.75))
                    })
                    print(f"موقعیت ثابت {i+1}: ({fixed_x}, {fixed_y})")

                # Also add the exact position from your images (speaker icon area)
                # Based on the crop images you sent, speaker is around (230, 880) for 540x960 resolution
                exact_x = int(230 * width / 540)  # Scale to current resolution (corrected from 80 to 230)
                exact_y = int(880 * height / 960)  # Scale to current resolution

                left_side_candidates.append({
                    'color': 'exact_scaled_position',
                    'pixels': 600,  # High priority
                    'position': (exact_x, exact_y),
                    'relative_position': (exact_x, exact_y - int(height * 0.75))
                })
                print(f"موقعیت دقیق مقیاس‌شده: ({exact_x}, {exact_y})")

            if not left_side_candidates:
                print("❌ هیچ ایکن بلندگویی یافت نشد")
                return False

            # Sort by pixel count (more pixels = more likely to be the icon)
            left_side_candidates.sort(key=lambda x: x['pixels'], reverse=True)

            best_speaker = left_side_candidates[0]
            click_x, click_y = best_speaker['position']

            print(f"🎯 انتخاب بهترین کاندید: {best_speaker['color']} در موقعیت ({click_x}, {click_y})")

            # Perform the click using adb
            adb_path = os.path.join(os.getcwd(), "adb", "adb.exe")

            # Check if adb exists
            if not os.path.exists(adb_path):
                try:
                    adb_version = subprocess.run(["adb", "version"], capture_output=True, text=True)
                    adb_path = "adb"
                except (subprocess.SubprocessError, FileNotFoundError):
                    raise Exception("adb.exe یافت نشد")

            # Perform click
            click_cmd = f'{adb_path} -s 127.0.0.1:{port_number} shell input tap {click_x} {click_y}'
            click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True)
            print(f"🔊 کلیک روی ایکن بلندگو انجام شد: {click_result.stdout}")

            # Wait a moment for the click to register
            time.sleep(1)
            return True

        except Exception as e:
            print(f"خطا در تشخیص ایکن بلندگو: {e}")
            return False


class WalletManagementWindow:
    def __init__(self, parent):
        self.parent = parent
        self.conn = parent.conn
        self.cursor = parent.cursor

        # Create the window
        self.window = tk.Toplevel(parent)
        self.window.title("💰 مدیریت آدرس‌های والت")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()

        # Center the window
        self.center_window()

        # Setup the interface
        self.setup_interface()

        # Load wallet addresses
        self.load_wallet_addresses()

    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    def setup_interface(self):
        """Setup the wallet management interface"""
        # Main frame
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="💰 مدیریت آدرس‌های والت",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Top frame for buttons and search
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill='x', pady=(0, 10))

        # Left side - Action buttons
        buttons_frame = ttk.Frame(top_frame)
        buttons_frame.pack(side='left', fill='x', expand=True)

        # Add wallet button
        self.add_btn = ttk.Button(buttons_frame, text="➕ افزودن آدرس جدید",
                                 command=self.add_wallet_address)
        self.add_btn.pack(side='right', padx=5)

        # Edit wallet button
        self.edit_btn = ttk.Button(buttons_frame, text="✏️ ویرایش",
                                  command=self.edit_wallet_address)
        self.edit_btn.pack(side='right', padx=5)

        # Delete wallet button
        self.delete_btn = ttk.Button(buttons_frame, text="🗑️ حذف",
                                    command=self.delete_wallet_address)
        self.delete_btn.pack(side='right', padx=5)

        # Load from file button
        self.load_btn = ttk.Button(buttons_frame, text="📁 بارگذاری از فایل",
                                  command=self.load_from_file)
        self.load_btn.pack(side='right', padx=5)

        # Export button
        self.export_btn = ttk.Button(buttons_frame, text="💾 خروجی",
                                    command=self.export_addresses)
        self.export_btn.pack(side='right', padx=5)

        # Right side - Search
        search_frame = ttk.Frame(top_frame)
        search_frame.pack(side='right')

        ttk.Label(search_frame, text="جستجو:").pack(side='right', padx=5)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side='right', padx=5)

        # Treeview frame
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill='both', expand=True, pady=10)

        # Create treeview
        columns = ('id', 'address', 'label', 'created', 'last_used', 'usage_count', 'status')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Define headings
        self.tree.heading('id', text='ردیف')
        self.tree.heading('address', text='آدرس والت')
        self.tree.heading('label', text='برچسب')
        self.tree.heading('created', text='تاریخ ایجاد')
        self.tree.heading('last_used', text='آخرین استفاده')
        self.tree.heading('usage_count', text='تعداد استفاده')
        self.tree.heading('status', text='وضعیت')

        # Configure column widths
        self.tree.column('id', width=50, anchor='center')
        self.tree.column('address', width=300, anchor='w')
        self.tree.column('label', width=120, anchor='w')
        self.tree.column('created', width=100, anchor='center')
        self.tree.column('last_used', width=100, anchor='center')
        self.tree.column('usage_count', width=80, anchor='center')
        self.tree.column('status', width=80, anchor='center')

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Bind double-click to edit
        self.tree.bind('<Double-1>', lambda e: self.edit_wallet_address())

        # Bottom frame for statistics
        stats_frame = ttk.LabelFrame(main_frame, text="آمار", padding=10)
        stats_frame.pack(fill='x', pady=(10, 0))

        self.stats_label = ttk.Label(stats_frame, text="در حال بارگذاری...")
        self.stats_label.pack()

        # Configure tags for treeview
        self.tree.tag_configure('active', background='#e8f5e8')
        self.tree.tag_configure('inactive', background='#ffe8e8')

    def validate_wallet_address(self, address):
        """Validate wallet address format"""
        # Clean the address
        address = address.strip()

        # Check if it starts with 0x
        if not address.startswith('0x'):
            return False, "آدرس والت باید با 0x شروع شود"

        # Check length (Ethereum addresses are 42 characters: 0x + 40 hex chars)
        if len(address) != 42:
            return False, "آدرس والت باید 42 کاراکتر باشد (0x + 40 کاراکتر هگزادسیمال)"

        # Check if all characters after 0x are valid hex
        hex_part = address[2:]
        if not all(c in '0123456789abcdefABCDEF' for c in hex_part):
            return False, "آدرس والت شامل کاراکترهای نامعتبر است"

        return True, "آدرس معتبر است"

    def clean_wallet_address(self, address):
        """Clean and normalize wallet address"""
        # Remove leading/trailing spaces
        address = address.strip()

        # Convert to lowercase for consistency
        address = address.lower()

        return address

    def load_wallet_addresses(self):
        """Load wallet addresses from database"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            # Get all wallet addresses
            self.cursor.execute("""
                SELECT id, address, label, created_date, last_used_date, usage_count, is_active
                FROM wallet_addresses
                ORDER BY created_date DESC, id DESC
            """)

            addresses = self.cursor.fetchall()

            # Add to treeview
            for i, addr in enumerate(addresses, 1):
                addr_id, address, label, created_date, last_used_date, usage_count, is_active = addr

                # Format dates
                created_display = created_date if created_date else "-"
                last_used_display = last_used_date if last_used_date else "-"

                # Status
                status = "فعال" if is_active else "غیرفعال"
                tag = 'active' if is_active else 'inactive'

                # Truncate address for display
                display_address = address[:10] + "..." + address[-8:] if len(address) > 20 else address

                values = (i, display_address, label or "-", created_display,
                         last_used_display, usage_count or 0, status)

                item = self.tree.insert('', 'end', values=values, tags=[tag])
                # Store full address in item for later use
                self.tree.set(item, 'full_address', address)
                self.tree.set(item, 'db_id', addr_id)

            # Update statistics
            self.update_statistics()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری آدرس‌ها: {e}")

    def update_statistics(self):
        """Update statistics label"""
        try:
            # Get counts
            self.cursor.execute("SELECT COUNT(*) FROM wallet_addresses")
            total_count = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM wallet_addresses WHERE is_active = 1")
            active_count = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT SUM(usage_count) FROM wallet_addresses")
            total_usage = self.cursor.fetchone()[0] or 0

            stats_text = f"مجموع آدرس‌ها: {total_count} | فعال: {active_count} | غیرفعال: {total_count - active_count} | مجموع استفاده: {total_usage}"
            self.stats_label.config(text=stats_text)

        except Exception as e:
            self.stats_label.config(text=f"خطا در محاسبه آمار: {e}")

    def on_search_change(self, *args):
        """Handle search text change"""
        search_text = self.search_var.get().lower()

        # If search is empty, reload all addresses
        if not search_text:
            self.load_wallet_addresses()
            return

        # Filter addresses based on search text
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            # Search in address and label
            self.cursor.execute("""
                SELECT id, address, label, created_date, last_used_date, usage_count, is_active
                FROM wallet_addresses
                WHERE LOWER(address) LIKE ? OR LOWER(label) LIKE ?
                ORDER BY created_date DESC, id DESC
            """, (f'%{search_text}%', f'%{search_text}%'))

            addresses = self.cursor.fetchall()

            # Add filtered results to treeview
            for i, addr in enumerate(addresses, 1):
                addr_id, address, label, created_date, last_used_date, usage_count, is_active = addr

                # Format dates
                created_display = created_date if created_date else "-"
                last_used_display = last_used_date if last_used_date else "-"

                # Status
                status = "فعال" if is_active else "غیرفعال"
                tag = 'active' if is_active else 'inactive'

                # Truncate address for display
                display_address = address[:10] + "..." + address[-8:] if len(address) > 20 else address

                values = (i, display_address, label or "-", created_display,
                         last_used_display, usage_count or 0, status)

                item = self.tree.insert('', 'end', values=values, tags=[tag])
                # Store full address in item for later use
                self.tree.set(item, 'full_address', address)
                self.tree.set(item, 'db_id', addr_id)

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در جستجو: {e}")

    def add_wallet_address(self):
        """Add new wallet address"""
        dialog = WalletAddressDialog(self.window, "افزودن آدرس جدید")
        if dialog.result:
            address, label, notes = dialog.result

            # Clean and validate address
            address = self.clean_wallet_address(address)
            is_valid, message = self.validate_wallet_address(address)

            if not is_valid:
                messagebox.showerror("خطا", message)
                return

            try:
                # Check if address already exists
                self.cursor.execute("SELECT id FROM wallet_addresses WHERE address = ?", (address,))
                if self.cursor.fetchone():
                    messagebox.showwarning("هشدار", "این آدرس قبلاً ثبت شده است.")
                    return

                # Insert new address
                from datetime import datetime
                now = datetime.now()
                created_date = now.strftime("%Y/%m/%d")
                created_time = now.strftime("%H:%M:%S")

                self.cursor.execute("""
                    INSERT INTO wallet_addresses (address, label, created_date, created_time, notes)
                    VALUES (?, ?, ?, ?, ?)
                """, (address, label, created_date, created_time, notes))

                self.conn.commit()
                messagebox.showinfo("موفقیت", "آدرس والت با موفقیت اضافه شد.")

                # Reload addresses
                self.load_wallet_addresses()

            except Exception as e:
                messagebox.showerror("خطا", f"خطا در افزودن آدرس: {e}")

    def edit_wallet_address(self):
        """Edit selected wallet address"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک آدرس را انتخاب کنید.")
            return

        item = selected[0]
        db_id = self.tree.set(item, 'db_id')
        full_address = self.tree.set(item, 'full_address')

        # Get current data
        try:
            self.cursor.execute("""
                SELECT address, label, notes, is_active
                FROM wallet_addresses WHERE id = ?
            """, (db_id,))

            result = self.cursor.fetchone()
            if not result:
                messagebox.showerror("خطا", "آدرس یافت نشد.")
                return

            current_address, current_label, current_notes, is_active = result

            dialog = WalletAddressDialog(self.window, "ویرایش آدرس",
                                       current_address, current_label, current_notes, is_active)
            if dialog.result:
                address, label, notes, new_is_active = dialog.result

                # Clean and validate address
                address = self.clean_wallet_address(address)
                is_valid, message = self.validate_wallet_address(address)

                if not is_valid:
                    messagebox.showerror("خطا", message)
                    return

                # Check if address already exists (excluding current record)
                self.cursor.execute("SELECT id FROM wallet_addresses WHERE address = ? AND id != ?",
                                  (address, db_id))
                if self.cursor.fetchone():
                    messagebox.showwarning("هشدار", "این آدرس قبلاً ثبت شده است.")
                    return

                # Update address
                self.cursor.execute("""
                    UPDATE wallet_addresses
                    SET address = ?, label = ?, notes = ?, is_active = ?
                    WHERE id = ?
                """, (address, label, notes, new_is_active, db_id))

                self.conn.commit()
                messagebox.showinfo("موفقیت", "آدرس والت با موفقیت ویرایش شد.")

                # Reload addresses
                self.load_wallet_addresses()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ویرایش آدرس: {e}")

    def delete_wallet_address(self):
        """Delete selected wallet address"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک آدرس را انتخاب کنید.")
            return

        item = selected[0]
        db_id = self.tree.set(item, 'db_id')
        full_address = self.tree.set(item, 'full_address')

        # Confirm deletion
        result = messagebox.askyesno("تأیید حذف",
                                   f"آیا مطمئن هستید که می‌خواهید این آدرس را حذف کنید؟\n\n{full_address}")
        if not result:
            return

        try:
            self.cursor.execute("DELETE FROM wallet_addresses WHERE id = ?", (db_id,))
            self.conn.commit()
            messagebox.showinfo("موفقیت", "آدرس والت با موفقیت حذف شد.")

            # Reload addresses
            self.load_wallet_addresses()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در حذف آدرس: {e}")

    def load_from_file(self):
        """Load wallet addresses from text file"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="انتخاب فایل آدرس‌های والت",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            # Process addresses
            valid_addresses = []
            invalid_addresses = []
            duplicate_addresses = []

            for line_num, line in enumerate(lines, 1):
                # Clean the line
                address = line.strip()

                # Skip empty lines
                if not address:
                    continue

                # Clean and validate address
                address = self.clean_wallet_address(address)
                is_valid, message = self.validate_wallet_address(address)

                if not is_valid:
                    invalid_addresses.append(f"خط {line_num}: {address} - {message}")
                    continue

                # Check if address already exists in database
                self.cursor.execute("SELECT id FROM wallet_addresses WHERE address = ?", (address,))
                if self.cursor.fetchone():
                    duplicate_addresses.append(f"خط {line_num}: {address}")
                    continue

                # Check if address already exists in current batch
                if address in [addr[0] for addr in valid_addresses]:
                    duplicate_addresses.append(f"خط {line_num}: {address} (تکراری در فایل)")
                    continue

                valid_addresses.append((address, f"بارگذاری شده از فایل - خط {line_num}"))

            # Show summary dialog
            summary_dialog = LoadSummaryDialog(self.window, valid_addresses, invalid_addresses, duplicate_addresses)
            if summary_dialog.result:
                # Insert valid addresses
                from datetime import datetime
                now = datetime.now()
                created_date = now.strftime("%Y/%m/%d")
                created_time = now.strftime("%H:%M:%S")

                inserted_count = 0
                for address, label in valid_addresses:
                    try:
                        self.cursor.execute("""
                            INSERT INTO wallet_addresses (address, label, created_date, created_time)
                            VALUES (?, ?, ?, ?)
                        """, (address, label, created_date, created_time))
                        inserted_count += 1
                    except Exception as e:
                        print(f"خطا در درج آدرس {address}: {e}")

                self.conn.commit()
                messagebox.showinfo("موفقیت", f"{inserted_count} آدرس با موفقیت اضافه شد.")

                # Reload addresses
                self.load_wallet_addresses()

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در بارگذاری فایل: {e}")

    def export_addresses(self):
        """Export wallet addresses to text file"""
        from tkinter import filedialog

        file_path = filedialog.asksaveasfilename(
            title="ذخیره آدرس‌های والت",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # Get all active addresses
            self.cursor.execute("""
                SELECT address, label FROM wallet_addresses
                WHERE is_active = 1
                ORDER BY created_date DESC
            """)

            addresses = self.cursor.fetchall()

            with open(file_path, 'w', encoding='utf-8') as file:
                for address, label in addresses:
                    if label:
                        file.write(f"{address}  # {label}\n")
                    else:
                        file.write(f"{address}\n")

            messagebox.showinfo("موفقیت", f"{len(addresses)} آدرس در فایل ذخیره شد.")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل: {e}")


class WalletAddressDialog:
    def __init__(self, parent, title, address="", label="", notes="", is_active=True):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.center_dialog()

        # Setup interface
        self.setup_interface(address, label, notes, is_active)

        # Wait for dialog to close
        self.dialog.wait_window()

    def center_dialog(self):
        """Center the dialog on screen"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    def setup_interface(self, address, label, notes, is_active):
        """Setup dialog interface"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill='both', expand=True)

        # Address field
        ttk.Label(main_frame, text="آدرس والت: *", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.address_var = tk.StringVar(value=address)
        address_entry = ttk.Entry(main_frame, textvariable=self.address_var, width=60)
        address_entry.pack(fill='x', pady=(0, 10))
        address_entry.focus()

        # Label field
        ttk.Label(main_frame, text="برچسب:", font=('Arial', 10)).pack(anchor='w', pady=(0, 5))
        self.label_var = tk.StringVar(value=label)
        label_entry = ttk.Entry(main_frame, textvariable=self.label_var, width=60)
        label_entry.pack(fill='x', pady=(0, 10))

        # Notes field
        ttk.Label(main_frame, text="یادداشت:", font=('Arial', 10)).pack(anchor='w', pady=(0, 5))
        notes_frame = ttk.Frame(main_frame)
        notes_frame.pack(fill='both', expand=True, pady=(0, 10))

        self.notes_text = tk.Text(notes_frame, height=6, wrap='word')
        notes_scrollbar = ttk.Scrollbar(notes_frame, orient='vertical', command=self.notes_text.yview)
        self.notes_text.configure(yscrollcommand=notes_scrollbar.set)
        self.notes_text.pack(side='left', fill='both', expand=True)
        notes_scrollbar.pack(side='right', fill='y')

        if notes:
            self.notes_text.insert('1.0', notes)

        # Status checkbox (only for edit mode)
        if address:  # Edit mode
            self.is_active_var = tk.BooleanVar(value=is_active)
            status_check = ttk.Checkbutton(main_frame, text="فعال", variable=self.is_active_var)
            status_check.pack(anchor='w', pady=(0, 10))
        else:
            self.is_active_var = tk.BooleanVar(value=True)

        # Validation info
        info_frame = ttk.LabelFrame(main_frame, text="راهنما", padding=10)
        info_frame.pack(fill='x', pady=(0, 10))

        info_text = ("• آدرس والت باید با 0x شروع شود\n"
                    "• آدرس باید دقیقاً 42 کاراکتر باشد\n"
                    "• فقط کاراکترهای هگزادسیمال مجاز هستند (0-9, a-f, A-F)")
        ttk.Label(info_frame, text=info_text, justify='right').pack(anchor='w')

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(button_frame, text="ذخیره", command=self.save).pack(side='right', padx=5)
        ttk.Button(button_frame, text="انصراف", command=self.cancel).pack(side='left', padx=5)

    def save(self):
        """Save the wallet address"""
        address = self.address_var.get().strip()
        label = self.label_var.get().strip()
        notes = self.notes_text.get('1.0', 'end-1c').strip()
        is_active = self.is_active_var.get()

        if not address:
            messagebox.showerror("خطا", "آدرس والت الزامی است.")
            return

        # Return result based on mode
        if hasattr(self, 'is_active_var') and self.address_var.get():  # Edit mode
            self.result = (address, label, notes, is_active)
        else:  # Add mode
            self.result = (address, label, notes)

        self.dialog.destroy()

    def cancel(self):
        """Cancel the dialog"""
        self.result = None
        self.dialog.destroy()


class LoadSummaryDialog:
    def __init__(self, parent, valid_addresses, invalid_addresses, duplicate_addresses):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("خلاصه بارگذاری فایل")
        self.dialog.geometry("700x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.center_dialog()

        # Setup interface
        self.setup_interface(valid_addresses, invalid_addresses, duplicate_addresses)

        # Wait for dialog to close
        self.dialog.wait_window()

    def center_dialog(self):
        """Center the dialog on screen"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    def setup_interface(self, valid_addresses, invalid_addresses, duplicate_addresses):
        """Setup dialog interface"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="خلاصه بارگذاری فایل", font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))

        # Summary stats
        stats_frame = ttk.LabelFrame(main_frame, text="آمار", padding=10)
        stats_frame.pack(fill='x', pady=(0, 10))

        stats_text = (f"✅ آدرس‌های معتبر: {len(valid_addresses)}\n"
                      f"❌ آدرس‌های نامعتبر: {len(invalid_addresses)}\n"
                      f"🔄 آدرس‌های تکراری: {len(duplicate_addresses)}")
        ttk.Label(stats_frame, text=stats_text, font=('Arial', 10)).pack(anchor='w')

        # Notebook for different categories
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, pady=(10, 0))

        # Valid addresses tab
        if valid_addresses:
            valid_frame = ttk.Frame(notebook)
            notebook.add(valid_frame, text=f"✅ معتبر ({len(valid_addresses)})")

            valid_text = tk.Text(valid_frame, wrap='word', height=10)
            valid_scrollbar = ttk.Scrollbar(valid_frame, orient='vertical', command=valid_text.yview)
            valid_text.configure(yscrollcommand=valid_scrollbar.set)
            valid_text.pack(side='left', fill='both', expand=True)
            valid_scrollbar.pack(side='right', fill='y')

            for address, label in valid_addresses:
                valid_text.insert('end', f"{address}\n")
            valid_text.config(state='disabled')

        # Invalid addresses tab
        if invalid_addresses:
            invalid_frame = ttk.Frame(notebook)
            notebook.add(invalid_frame, text=f"❌ نامعتبر ({len(invalid_addresses)})")

            invalid_text = tk.Text(invalid_frame, wrap='word', height=10)
            invalid_scrollbar = ttk.Scrollbar(invalid_frame, orient='vertical', command=invalid_text.yview)
            invalid_text.configure(yscrollcommand=invalid_scrollbar.set)
            invalid_text.pack(side='left', fill='both', expand=True)
            invalid_scrollbar.pack(side='right', fill='y')

            for invalid in invalid_addresses:
                invalid_text.insert('end', f"{invalid}\n")
            invalid_text.config(state='disabled')

        # Duplicate addresses tab
        if duplicate_addresses:
            duplicate_frame = ttk.Frame(notebook)
            notebook.add(duplicate_frame, text=f"🔄 تکراری ({len(duplicate_addresses)})")

            duplicate_text = tk.Text(duplicate_frame, wrap='word', height=10)
            duplicate_scrollbar = ttk.Scrollbar(duplicate_frame, orient='vertical', command=duplicate_text.yview)
            duplicate_text.configure(yscrollcommand=duplicate_scrollbar.set)
            duplicate_text.pack(side='left', fill='both', expand=True)
            duplicate_scrollbar.pack(side='right', fill='y')

            for duplicate in duplicate_addresses:
                duplicate_text.insert('end', f"{duplicate}\n")
            duplicate_text.config(state='disabled')

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))

        if valid_addresses:
            ttk.Button(button_frame, text=f"ادامه و درج {len(valid_addresses)} آدرس",
                      command=self.proceed).pack(side='right', padx=5)
        ttk.Button(button_frame, text="انصراف", command=self.cancel).pack(side='left', padx=5)

    def proceed(self):
        """Proceed with loading valid addresses"""
        self.result = True
        self.dialog.destroy()

    def cancel(self):
        """Cancel the loading"""
        self.result = False
        self.dialog.destroy()


class WalletConnectionWindow:
    def __init__(self, parent):
        self.parent = parent

        # Create the window
        self.window = tk.Toplevel(parent)
        self.window.title("🔗 ارتباط با والت - تحلیل تراکنش‌های BNB")
        self.window.geometry("1400x800")  # Increased size for full addresses
        self.window.resizable(True, True)
        self.window.transient(parent)
        # Remove grab_set to allow clipboard operations
        # self.window.grab_set()

        # Center the window
        self.center_window()

        # Initialize variables
        self.transactions = []
        self.unique_addresses = set()

        # Setup the interface
        self.setup_interface()

    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    def setup_interface(self):
        """Setup the wallet connection interface"""
        # Main frame
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🔗 ارتباط با والت - تحلیل تراکنش‌های BNB",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Input frame
        input_frame = ttk.LabelFrame(main_frame, text="آدرس والت BNB", padding=10)
        input_frame.pack(fill='x', pady=(0, 10))

        # Address input
        address_input_frame = ttk.Frame(input_frame)
        address_input_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(address_input_frame, text="آدرس والت:", font=('Arial', 10, 'bold')).pack(side='right', padx=5)
        self.address_var = tk.StringVar()
        self.address_entry = ttk.Entry(address_input_frame, textvariable=self.address_var, width=50)
        self.address_entry.pack(side='right', padx=5, fill='x', expand=True)
        self.address_entry.focus()

        # Enable clipboard operations for address entry
        self.setup_entry_clipboard(self.address_entry)

        # Buttons frame
        buttons_input_frame = ttk.Frame(input_frame)
        buttons_input_frame.pack(fill='x')

        # Paste button
        paste_btn = ttk.Button(buttons_input_frame, text="📋 Paste",
                              command=self.paste_address)
        paste_btn.pack(side='right', padx=5)

        # Analyze button
        self.analyze_btn = ttk.Button(buttons_input_frame, text="🔍 تحلیل تراکنش‌ها",
                                     command=self.analyze_transactions)
        self.analyze_btn.pack(side='right', padx=5)

        # Clear button
        clear_btn = ttk.Button(buttons_input_frame, text="🗑️ پاک کردن",
                              command=self.clear_results)
        clear_btn.pack(side='right', padx=5)

        # Export button
        self.export_btn = ttk.Button(buttons_input_frame, text="💾 خروجی",
                                    command=self.export_results)
        self.export_btn.pack(side='right', padx=5)
        self.export_btn.config(state='disabled')

        # Copy button
        self.copy_btn = ttk.Button(buttons_input_frame, text="📋 کپی انتخاب شده",
                                  command=self.copy_selected)
        self.copy_btn.pack(side='right', padx=5)
        self.copy_btn.config(state='disabled')

        # Progress frame
        self.progress_frame = ttk.Frame(main_frame)
        self.progress_frame.pack(fill='x', pady=(0, 10))

        self.progress_label = ttk.Label(self.progress_frame, text="")
        self.progress_label.pack(side='right')

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill='x', padx=(0, 10))

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="نتایج تحلیل", padding=5)
        results_frame.pack(fill='both', expand=True, pady=(0, 10))

        # Treeview frame
        tree_frame = ttk.Frame(results_frame)
        tree_frame.pack(fill='both', expand=True)

        # Create treeview
        columns = ('row', 'address', 'type', 'amount', 'hash', 'date', 'full_address', 'full_hash')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Define headings with sorting capability
        self.tree.heading('row', text='ردیف', command=lambda: self.sort_column('row'))
        self.tree.heading('address', text='آدرس والت', command=lambda: self.sort_column('address'))
        self.tree.heading('type', text='نوع تراکنش', command=lambda: self.sort_column('type'))
        self.tree.heading('amount', text='مقدار KKT', command=lambda: self.sort_column('amount'))
        self.tree.heading('hash', text='هش تراکنش', command=lambda: self.sort_column('hash'))
        self.tree.heading('date', text='تاریخ', command=lambda: self.sort_column('date'))

        # Configure column widths
        self.tree.column('row', width=50, anchor='center')
        self.tree.column('address', width=400, anchor='w')  # Increased width for full address
        self.tree.column('type', width=100, anchor='center')
        self.tree.column('amount', width=120, anchor='e')
        self.tree.column('hash', width=500, anchor='w')  # Increased width for full hash
        self.tree.column('date', width=120, anchor='center')

        # Hide the full_address and full_hash columns (used only for data storage)
        self.tree.column('full_address', width=0, stretch=False)
        self.tree.column('full_hash', width=0, stretch=False)
        self.tree.heading('full_address', text='')
        self.tree.heading('full_hash', text='')

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Configure tags for treeview
        self.tree.tag_configure('incoming', background='#e8f5e8')  # Light green for incoming
        self.tree.tag_configure('outgoing', background='#ffe8e8')  # Light red for outgoing

        # Bottom frame for statistics
        stats_frame = ttk.LabelFrame(main_frame, text="آمار", padding=10)
        stats_frame.pack(fill='x')

        self.stats_label = ttk.Label(stats_frame, text="آماده برای تحلیل...")
        self.stats_label.pack()

        # Bind events
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)  # Right click
        self.tree.bind('<Control-c>', self.copy_selected_keyboard)  # Ctrl+C
        self.window.bind('<Control-c>', self.copy_selected_keyboard)  # Ctrl+C for window

        # Create context menu
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="📋 کپی آدرس", command=self.copy_address)
        self.context_menu.add_command(label="📋 کپی هش", command=self.copy_hash)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔍 جزئیات تراکنش", command=self.show_transaction_details)

        # Sort variables
        self.sort_reverse = {}

    def setup_entry_clipboard(self, entry):
        """Setup clipboard operations for entry widget"""
        def paste_text(event=None):
            try:
                # Get text from clipboard
                clipboard_text = self.window.clipboard_get()
                # Clear current selection and insert clipboard text
                if entry.selection_present():
                    entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                else:
                    entry.delete(0, tk.END)
                entry.insert(tk.INSERT, clipboard_text)
                return "break"  # Prevent default behavior
            except tk.TclError:
                # Clipboard is empty or unavailable
                pass

        def copy_text(event=None):
            try:
                if entry.selection_present():
                    selected_text = entry.selection_get()
                    self.window.clipboard_clear()
                    self.window.clipboard_append(selected_text)
                return "break"
            except tk.TclError:
                pass

        def cut_text(event=None):
            try:
                if entry.selection_present():
                    selected_text = entry.selection_get()
                    self.window.clipboard_clear()
                    self.window.clipboard_append(selected_text)
                    entry.delete(tk.SEL_FIRST, tk.SEL_LAST)
                return "break"
            except tk.TclError:
                pass

        def select_all(event=None):
            entry.select_range(0, tk.END)
            return "break"

        # Bind clipboard operations
        entry.bind('<Control-v>', paste_text)
        entry.bind('<Control-c>', copy_text)
        entry.bind('<Control-x>', cut_text)
        entry.bind('<Control-a>', select_all)

        # Also bind right-click context menu
        def show_entry_context_menu(event):
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="📋 Paste (Ctrl+V)", command=lambda: paste_text())
            context_menu.add_command(label="📄 Copy (Ctrl+C)", command=lambda: copy_text())
            context_menu.add_command(label="✂️ Cut (Ctrl+X)", command=lambda: cut_text())
            context_menu.add_separator()
            context_menu.add_command(label="🔘 Select All (Ctrl+A)", command=lambda: select_all())

            try:
                context_menu.post(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        entry.bind('<Button-3>', show_entry_context_menu)

    def paste_address(self):
        """Paste address from clipboard"""
        try:
            clipboard_text = self.window.clipboard_get()
            # Clean the clipboard text
            clipboard_text = clipboard_text.strip()
            # Clear current content and insert clipboard text
            self.address_var.set(clipboard_text)
            self.address_entry.focus()
        except tk.TclError:
            # Clipboard is empty or unavailable
            messagebox.showwarning("هشدار", "کلیپ‌بورد خالی است یا در دسترس نیست.")

    def analyze_transactions(self):
        """Analyze transactions for the given wallet address"""
        address = self.address_var.get().strip()

        if not address:
            messagebox.showerror("خطا", "لطفاً آدرس والت را وارد کنید.")
            return

        # Validate address format
        if not self.validate_bnb_address(address):
            messagebox.showerror("خطا", "فرمت آدرس والت BNB نامعتبر است.")
            return

        # Start analysis in a separate thread
        import threading
        analysis_thread = threading.Thread(target=self.perform_analysis, args=(address,))
        analysis_thread.daemon = True
        analysis_thread.start()

    def validate_bnb_address(self, address):
        """Validate BNB address format"""
        # BNB addresses start with 'bnb' or '0x' and have specific lengths
        if address.startswith('bnb') and len(address) == 42:
            return True
        elif address.startswith('0x') and len(address) == 42:
            return True
        return False

    def perform_analysis(self, address):
        """Perform the actual transaction analysis"""
        try:
            # Update UI to show progress
            self.window.after(0, lambda: self.start_progress("در حال اتصال به شبکه BNB..."))

            # Get transactions from BscScan API
            transactions = self.get_kkt_transactions(address)

            # Update UI with results
            self.window.after(0, lambda: self.display_results(transactions))

        except Exception as e:
            error_message = str(e)
            self.window.after(0, lambda msg=error_message: self.show_error(f"خطا در تحلیل: {msg}"))

    def get_kkt_transactions(self, address):
        """Get KKT token transactions from BscScan API"""
        try:
            import requests
        except ImportError:
            # If requests is not available, use sample data
            return self.get_sample_transactions(address)

        from datetime import datetime
        import json

        # KKT token contract address
        kkt_contract = "0xe64017bdacbe7dfc84886c3704a26d566e7550de"

        # BscScan API endpoint
        api_key = "YourBscScanAPIKey"  # Replace with actual API key
        base_url = "https://api.bscscan.com/api"

        transactions = []
        unique_addresses = set()

        try:
            # Update progress
            self.window.after(0, lambda: self.update_progress("در حال دریافت تراکنش‌ها..."))

            # For demo purposes, we'll use sample data
            # To use real API, set use_real_api = True and provide valid API key
            use_real_api = False

            if use_real_api and api_key != "YourBscScanAPIKey":
                # Get token transfers from real API
                params = {
                    'module': 'account',
                    'action': 'tokentx',
                    'contractaddress': kkt_contract,
                    'address': address,
                    'page': 1,
                    'offset': 1000,
                    'sort': 'desc',
                    'apikey': api_key
                }

                response = requests.get(base_url, params=params, timeout=30)
                data = response.json()

                if data['status'] == '1':
                    sample_transactions = data['result']
                else:
                    # Fallback to sample data if API fails
                    return self.get_sample_transactions(address)
            else:
                # Use sample data for demonstration
                return self.get_sample_transactions(address)

        except Exception as e:
            print(f"Error fetching from API: {e}")
            # Return sample data on error
            return self.get_sample_transactions(address)

        return transactions

    def get_sample_transactions(self, address):
        """Get sample transaction data for demonstration"""
        from datetime import datetime

        # Sample data for demonstration with realistic wallet addresses
        sample_transactions = [
            {
                'from': '******************************************',  # Binance Hot Wallet
                'to': address,
                'value': '1000000000000000000000',  # 1000 KKT
                'hash': '0xa1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
                'timeStamp': '1640995200'  # Example timestamp
            },
            {
                'from': address,
                'to': '******************************************',  # Binance Hot Wallet 2
                'value': '500000000000000000000',   # 500 KKT
                'hash': '0xb2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a',
                'timeStamp': '1640908800'  # Example timestamp
            },
            {
                'from': '******************************************',  # Binance Hot Wallet 3
                'to': address,
                'value': '2000000000000000000000',  # 2000 KKT
                'hash': '0xc3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2',
                'timeStamp': '1640822400'  # Example timestamp
            },
            {
                'from': '******************************************',  # Binance Hot Wallet 4
                'to': address,
                'value': '750000000000000000000',   # 750 KKT
                'hash': '0xd4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2c3',
                'timeStamp': '1640736000'  # Example timestamp
            },
            {
                'from': address,
                'to': '******************************************',  # Binance Hot Wallet 5
                'value': '300000000000000000000',   # 300 KKT
                'hash': '0xe5f6789012345678901234567890abcdef1234567890abcdef1234567ab2c3d4',
                'timeStamp': '1640649600'  # Example timestamp
            },
            {
                'from': '******************************************',  # Binance Hot Wallet 6
                'to': address,
                'value': '1250000000000000000000',  # 1250 KKT
                'hash': '0xf6789012345678901234567890abcdef1234567890abcdef1234567ab2c3d4e5',
                'timeStamp': '1640563200'  # Example timestamp
            }
        ]

        transactions = []
        unique_addresses = set()

        # Process transactions
        for i, tx in enumerate(sample_transactions):
            current_i = i + 1
            self.window.after(0, lambda idx=current_i: self.update_progress(f"پردازش تراکنش {idx}..."))

            from_addr = tx['from'].lower()
            to_addr = tx['to'].lower()
            target_addr = address.lower()

            # Determine transaction type and related address
            if from_addr == target_addr:
                # Outgoing transaction
                tx_type = "انتقال"
                related_address = to_addr
                tag = 'outgoing'
            else:
                # Incoming transaction
                tx_type = "واریزی"
                related_address = from_addr
                tag = 'incoming'

            # Skip if address already processed (avoid duplicates)
            if related_address in unique_addresses:
                continue

            unique_addresses.add(related_address)

            # Convert value from wei to KKT (assuming 18 decimals)
            amount_wei = int(tx['value'])
            amount_kkt = amount_wei / (10 ** 18)

            # Convert timestamp to readable date
            timestamp = int(tx['timeStamp'])
            date_str = datetime.fromtimestamp(timestamp).strftime('%Y/%m/%d')

            # Show full hash and address (no truncation)
            hash_display = tx['hash']
            addr_display = related_address

            transactions.append({
                'address': related_address,
                'address_display': addr_display,
                'type': tx_type,
                'amount': f"{amount_kkt:,.2f}",
                'hash': tx['hash'],
                'hash_display': hash_display,
                'date': date_str,
                'tag': tag
            })

        return transactions

    def start_progress(self, message):
        """Start progress indication"""
        self.progress_label.config(text=message)
        self.progress_bar.start()
        self.analyze_btn.config(state='disabled')

    def update_progress(self, message):
        """Update progress message"""
        self.progress_label.config(text=message)

    def stop_progress(self):
        """Stop progress indication"""
        self.progress_bar.stop()
        self.progress_label.config(text="")
        self.analyze_btn.config(state='normal')

    def display_results(self, transactions):
        """Display transaction results in treeview"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        self.transactions = transactions

        # Add transactions to treeview
        for i, tx in enumerate(transactions, 1):
            values = (i, tx['address_display'], tx['type'], tx['amount'],
                     tx['hash_display'], tx['date'])

            item = self.tree.insert('', 'end', values=values, tags=[tx['tag']])
            # Store full data in item
            self.tree.set(item, 'full_address', tx['address'])
            self.tree.set(item, 'full_hash', tx['hash'])

        # Update statistics
        self.update_statistics()

        # Enable buttons
        self.export_btn.config(state='normal')
        self.copy_btn.config(state='normal')

        # Stop progress
        self.stop_progress()

    def update_statistics(self):
        """Update statistics label"""
        total_count = len(self.transactions)
        incoming_count = len([tx for tx in self.transactions if tx['tag'] == 'incoming'])
        outgoing_count = len([tx for tx in self.transactions if tx['tag'] == 'outgoing'])

        total_incoming = sum(float(tx['amount'].replace(',', '')) for tx in self.transactions if tx['tag'] == 'incoming')
        total_outgoing = sum(float(tx['amount'].replace(',', '')) for tx in self.transactions if tx['tag'] == 'outgoing')

        stats_text = (f"مجموع آدرس‌ها: {total_count} | "
                     f"واریزی: {incoming_count} ({total_incoming:,.2f} KKT) | "
                     f"انتقال: {outgoing_count} ({total_outgoing:,.2f} KKT)")

        self.stats_label.config(text=stats_text)

    def show_error(self, message):
        """Show error message"""
        self.stop_progress()
        messagebox.showerror("خطا", message)

    def clear_results(self):
        """Clear all results"""
        # Clear treeview
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Clear data
        self.transactions = []
        self.unique_addresses = set()

        # Reset UI
        self.stats_label.config(text="آماده برای تحلیل...")
        self.export_btn.config(state='disabled')
        self.copy_btn.config(state='disabled')

    def sort_column(self, col):
        """Sort treeview by column"""
        # Get current sort direction
        reverse = self.sort_reverse.get(col, False)
        self.sort_reverse[col] = not reverse

        # Get all items with their values
        items = [(self.tree.set(item, col), item) for item in self.tree.get_children()]

        # Sort items
        if col in ['row', 'amount']:
            # Numeric sort
            items.sort(key=lambda x: float(x[0].replace(',', '')) if x[0].replace(',', '').replace('.', '').isdigit() else 0, reverse=reverse)
        else:
            # String sort
            items.sort(key=lambda x: x[0], reverse=reverse)

        # Rearrange items in treeview
        for index, (val, item) in enumerate(items):
            self.tree.move(item, '', index)

        # Update row numbers
        for i, item in enumerate(self.tree.get_children(), 1):
            self.tree.set(item, 'row', i)

    def on_double_click(self, event):
        """Handle double-click on treeview item"""
        self.show_transaction_details()

    def show_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under cursor
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def copy_address(self):
        """Copy selected address to clipboard"""
        selected = self.tree.selection()
        if selected:
            item = selected[0]
            full_address = self.tree.set(item, 'full_address')
            self.window.clipboard_clear()
            self.window.clipboard_append(full_address)

    def copy_hash(self):
        """Copy selected transaction hash to clipboard"""
        selected = self.tree.selection()
        if selected:
            item = selected[0]
            full_hash = self.tree.set(item, 'full_hash')
            self.window.clipboard_clear()
            self.window.clipboard_append(full_hash)

    def copy_selected(self):
        """Copy selected items to clipboard"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک یا چند آیتم را انتخاب کنید.")
            return

        # Prepare data for copying
        data = []
        for item in selected:
            values = self.tree.item(item)['values']
            full_address = self.tree.set(item, 'full_address')
            full_hash = self.tree.set(item, 'full_hash')

            # Format: Row, Full Address, Type, Amount, Full Hash, Date
            row_data = f"{values[0]}\t{full_address}\t{values[2]}\t{values[3]}\t{full_hash}\t{values[5]}"
            data.append(row_data)

        # Copy to clipboard
        clipboard_text = "\n".join(data)
        self.window.clipboard_clear()
        self.window.clipboard_append(clipboard_text)

    def copy_selected_keyboard(self, event=None):
        """Copy selected items to clipboard using keyboard shortcut"""
        self.copy_selected()

    def show_transaction_details(self):
        """Show detailed transaction information"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("هشدار", "لطفاً یک تراکنش را انتخاب کنید.")
            return

        item = selected[0]
        values = self.tree.item(item)['values']
        full_address = self.tree.set(item, 'full_address')
        full_hash = self.tree.set(item, 'full_hash')

        # Create details dialog
        details_dialog = tk.Toplevel(self.window)
        details_dialog.title("جزئیات تراکنش")
        details_dialog.geometry("600x400")
        details_dialog.resizable(False, False)
        details_dialog.transient(self.window)
        details_dialog.grab_set()

        # Center the dialog
        details_dialog.update_idletasks()
        width = details_dialog.winfo_width()
        height = details_dialog.winfo_height()
        x = (details_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (details_dialog.winfo_screenheight() // 2) - (height // 2)
        details_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Main frame
        main_frame = ttk.Frame(details_dialog, padding=20)
        main_frame.pack(fill='both', expand=True)

        # Title
        ttk.Label(main_frame, text="🔍 جزئیات تراکنش", font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Details
        details_frame = ttk.LabelFrame(main_frame, text="اطلاعات", padding=10)
        details_frame.pack(fill='both', expand=True, pady=(0, 10))

        # Create text widget for details
        details_text = tk.Text(details_frame, wrap='word', height=15, font=('Consolas', 10))
        details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=details_text.yview)
        details_text.configure(yscrollcommand=details_scrollbar.set)
        details_text.pack(side='left', fill='both', expand=True)
        details_scrollbar.pack(side='right', fill='y')

        # Insert details
        details_content = f"""ردیف: {values[0]}

آدرس والت:
{full_address}

نوع تراکنش: {values[2]}

مقدار KKT: {values[3]}

هش تراکنش:
{full_hash}

تاریخ: {values[5]}

توضیحات:
• این تراکنش در شبکه BNB Smart Chain انجام شده است
• توکن: KKT (KepithorStudios Token)
• برای مشاهده جزئیات بیشتر می‌توانید از BscScan استفاده کنید
"""

        details_text.insert('1.0', details_content)
        details_text.config(state='disabled')

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(button_frame, text="📋 کپی آدرس",
                  command=lambda: self.copy_to_clipboard(full_address, "آدرس")).pack(side='right', padx=5)
        ttk.Button(button_frame, text="📋 کپی هش",
                  command=lambda: self.copy_to_clipboard(full_hash, "هش")).pack(side='right', padx=5)
        ttk.Button(button_frame, text="بستن", command=details_dialog.destroy).pack(side='left', padx=5)

    def copy_to_clipboard(self, text, label):
        """Copy text to clipboard"""
        self.window.clipboard_clear()
        self.window.clipboard_append(text)

    def export_results(self):
        """Export results to file"""
        if not self.transactions:
            messagebox.showwarning("هشدار", "هیچ داده‌ای برای خروجی وجود ندارد.")
            return

        from tkinter import filedialog

        file_path = filedialog.asksaveasfilename(
            title="ذخیره نتایج تحلیل",
            defaultextension=".txt",
            filetypes=[
                ("Text files", "*.txt"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )

        if not file_path:
            return

        try:
            # Determine file format
            if file_path.lower().endswith('.csv'):
                self.export_to_csv(file_path)
            else:
                self.export_to_txt(file_path)

            messagebox.showinfo("موفقیت", f"نتایج در فایل ذخیره شد:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطا", f"خطا در ذخیره فایل: {e}")

    def export_to_txt(self, file_path):
        """Export results to text file"""
        with open(file_path, 'w', encoding='utf-8') as file:
            # Header
            file.write("🔗 نتایج تحلیل ارتباط با والت\n")
            file.write("=" * 50 + "\n\n")

            # Statistics
            total_count = len(self.transactions)
            incoming_count = len([tx for tx in self.transactions if tx['tag'] == 'incoming'])
            outgoing_count = len([tx for tx in self.transactions if tx['tag'] == 'outgoing'])

            file.write(f"آمار کلی:\n")
            file.write(f"مجموع آدرس‌ها: {total_count}\n")
            file.write(f"واریزی: {incoming_count}\n")
            file.write(f"انتقال: {outgoing_count}\n\n")

            # Transactions
            file.write("جزئیات تراکنش‌ها:\n")
            file.write("-" * 50 + "\n\n")

            for i, tx in enumerate(self.transactions, 1):
                file.write(f"ردیف: {i}\n")
                file.write(f"آدرس: {tx['address']}\n")
                file.write(f"نوع: {tx['type']}\n")
                file.write(f"مقدار: {tx['amount']} KKT\n")
                file.write(f"هش: {tx['hash']}\n")
                file.write(f"تاریخ: {tx['date']}\n")
                file.write("-" * 30 + "\n\n")

    def export_to_csv(self, file_path):
        """Export results to CSV file"""
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)

            # Header
            writer.writerow(['ردیف', 'آدرس والت', 'نوع تراکنش', 'مقدار KKT', 'هش تراکنش', 'تاریخ'])

            # Data
            for i, tx in enumerate(self.transactions, 1):
                writer.writerow([i, tx['address'], tx['type'], tx['amount'], tx['hash'], tx['date']])


if __name__ == "__main__":
    # Check if Tesseract OCR is installed correctly
    tesseract_ok = check_tesseract_installation()
    if not tesseract_ok:
        print("Warning: Tesseract OCR is not installed correctly or not found.")
        print("OCR functionality may not work properly.")
        print("Please install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")

        # Show warning message
        import tkinter.messagebox as messagebox
        messagebox.showwarning(
            "Tesseract OCR Not Found",
            "Tesseract OCR is not installed correctly or not found.\n\n"
            "OCR functionality may not work properly.\n\n"
            "Please install Tesseract OCR from:\n"
            "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
            "After installation, make sure the path is set correctly in the code."
        )

    app = OCRApplication()
    app.mainloop()
